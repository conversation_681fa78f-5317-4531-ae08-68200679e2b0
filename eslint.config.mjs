import js from '@eslint/js';
import globals from 'globals';
import htmlPlugin from 'eslint-plugin-html';
import importPlugin from 'eslint-plugin-import';
import jestPlugin from 'eslint-plugin-jest';
import promisePlugin from 'eslint-plugin-promise';
import es5Plugin from 'eslint-plugin-es5';

const baseConfig = {
  languageOptions: {
    ...js.configs.recommended.languageOptions,
    globals: {
      console: 'readonly',
    },
  },
  plugins: {
    import: importPlugin,
    promise: promisePlugin,
  },
  rules: {
    ...js.configs.recommended.rules,
    ...importPlugin.flatConfigs.recommended.rules,
    ...promisePlugin.configs['flat/recommended'].rules,
    curly: ['error', 'all'],
    eqeqeq: ['error', 'always'],
    'import/no-extraneous-dependencies': [
      'error',
      { devDependencies: ['eslint.config.mjs', 'src/**/*.js', '**/*.test.js', '**/*.spec.js'] },
    ],
    'no-console': 'error',
    'no-new-func': 'error',
    'no-new-wrappers': 'error',
    'no-param-reassign': 'error',
    'no-unused-vars': [
      'error',
      {
        vars: 'all',
        args: 'all',
        ignoreRestSiblings: false,
        argsIgnorePattern: '^_+$', // Ignore parameters that are exactly underscore
        varsIgnorePattern: '^_+$', // Ignore variables that are exactly underscore
        caughtErrorsIgnorePattern: '^_+$', // Ignore caught errors that are exactly underscore
      },
    ],
    // Disabled because it conflicts with the convention of using `_` for unused parameters.
    'promise/param-names': 'off',
  },
};

// Base "latest" config for the node build scripts and root files
const nodeLatestConfig = {
  ...baseConfig,
  languageOptions: {
    ...baseConfig.languageOptions,
    ecmaVersion: 'latest',
    globals: {
      ...baseConfig.languageOptions.globals,
      ...globals.node,
    },
  },
  rules: {
    ...baseConfig.rules,
    'no-var': 'error',
    'prefer-const': 'error',
  },
};

// Add specific ES6+ methods / Web APIs to ban on both scripts and background services
const es5NoRestrictedSyntax = [
  {
    selector:
      "CallExpression[callee.name='fetch'], CallExpression[callee.object.name='window'][callee.property.name='fetch']",
    message: 'fetch is not supported on Samsung 2016',
  },
  {
    selector:
      "NewExpression[callee.name='URL'], NewExpression[callee.object.name='window'][callee.property.name='URL']",
    message: 'URL is not supported on Samsung 2016',
  },
  {
    selector: "CallExpression[callee.property.name='catch']",
    message: 'Use of .catch() is not allowed: use the second argument to then() instead',
  },
  {
    selector: "CallExpression[callee.property.name='finally']",
    message: 'Use of .finally() is not allowed',
  },
];

// Base ES5 config for both scripts and background services
// If you have a warning or an error in Tizen Studio, add a rule here to help devs to avoid it.
const es5Config = {
  ...baseConfig,
  plugins: {
    ...baseConfig.plugins,
    es5: es5Plugin,
  },
  languageOptions: {
    ...baseConfig.languageOptions,
    ecmaVersion: 5,
    sourceType: 'script',
    globals: {
      ...baseConfig.languageOptions.globals,
      document: 'readonly',
      tizen: 'readonly',
      webapis: 'readonly',
      window: 'readonly',
      XMLHttpRequest: 'readonly',
    },
  },
  rules: {
    ...baseConfig.rules,
    ...es5Plugin.configs['no-es2015'].rules,
    'no-bitwise': 'error',
    'no-restricted-syntax': ['error', ...es5NoRestrictedSyntax],
    'operator-linebreak': ['error', 'after'],
    'promise/catch-or-return': ['error', { allowThen: true }],
  },
};

export default [
  // 1. Global ignores
  {
    name: 'global:ignores',
    ignores: ['node_modules/', 'dist/', 'bin/', '.history/', 'resources/css/**/*.css'],
  },
  // 2. Configuration for the ESLint config file itself
  {
    name: 'config:eslint',
    files: ['eslint.config.mjs'],
    ...nodeLatestConfig,
    languageOptions: {
      ...nodeLatestConfig.languageOptions,
      sourceType: 'module',
    },
  },
  // 3. Root-level config files
  {
    name: 'config:root-files',
    files: ['*.js'],
    ...nodeLatestConfig,
    languageOptions: {
      ...nodeLatestConfig.languageOptions,
      sourceType: 'commonjs',
    },
  },
  // 4. Build scripts
  {
    name: 'source:build-scripts',
    // TODO: consider renaming `src` as `build-scripts`
    files: ['src/**/*.js'],
    ...nodeLatestConfig,
    languageOptions: {
      ...nodeLatestConfig.languageOptions,
      sourceType: 'commonjs',
    },
    rules: {
      ...nodeLatestConfig.rules,
      'no-console': 'off',
    },
  },
  // 5. JS scripts intended for Samsung 2016+ devices (ES5 compatible)
  {
    name: 'source:js/samsung-2016-compat',
    files: ['resources/scripts/**/*.js', 'resources/html/**/*.html'],
    ignores: [
      'resources/scripts/**/*.test.js',
      'resources/scripts/**/*.spec.js',
      'resources/scripts/**/__mocks__/**/*.js',
      'resources/scripts/**/__tests__/**/*.js',
      'resources/scripts/**/__test-utils__/**/*.js',
    ],
    ...es5Config,
    rules: {
      ...es5Config.rules,
      curly: ['error', 'all'],
      'no-restricted-syntax': [
        'error',
        ...es5NoRestrictedSyntax,
        {
          selector:
            "NewExpression[callee.name='Promise'], NewExpression[callee.object.name='window'][callee.property.name='Promise']",
          message: 'Promise is not supported on Samsung 2016',
        },
        {
          selector:
            "CallExpression[callee.object.name='Promise'], CallExpression[callee.object.object.name='window'][callee.object.property.name='Promise']",
          message: 'Promise is not supported on Samsung 2016',
        },
      ],
    },
  },
  // 5a. Override for linting inline scripts in HTML files
  {
    name: 'source:html/inline-scripts',
    files: ['resources/html/**/*.html'],
    ...es5Config,
    plugins: {
      ...es5Config.plugins,
      html: htmlPlugin,
    },
  },
  // 6. Background Services:
  // The modules loaded via xml (eden_service.js and cw_service.js) are executed in a different context.
  // In this context, Samsung 2016 provides a partial support for ES6 features such as Promises, but not all of them.
  // Therefore, we still need to use ES5 to avoid errors in Samsung 2016.
  {
    name: 'source:js/background-services',
    // TODO: consider moving both to `resources/background-services` and `resources/background-services/utils`
    files: ['resources/js/**/*.js', 'resources/utils/**/*.js'],
    ...es5Config,
    languageOptions: {
      ...es5Config.languageOptions,
      ecmaVersion: 5,
      sourceType: 'commonjs',
      globals: {
        ...es5Config.languageOptions.globals,
        Promise: 'readonly', // The Promise API is available in Samsung 2016, but not all of its features.
        tizen: 'readonly',
        webapis: 'readonly',
      },
    },
  },
  // 7. Jest-specific configurations
  {
    name: 'test:jest',
    files: [
      'src/**/*.test.js',
      'src/**/__tests__/**/*.js',
      'src/**/__test-utils__/**/*.js',
      'src/**/__mocks__/**/*.js',
      'resources/**/**/*.test.js',
      'resources/**/**/__tests__/**/*.js',
      'resources/**/**/__test-utils__/**/*.js',
      'resources/**/**/__mocks__/**/*.js',
    ],
    ...nodeLatestConfig,
    languageOptions: {
      ...nodeLatestConfig.languageOptions,
      sourceType: 'commonjs',
      globals: {
        ...nodeLatestConfig.languageOptions.globals,
        ...globals.jest,
      },
    },
    plugins: {
      ...nodeLatestConfig.plugins,
      jest: jestPlugin,
    },
    rules: {
      ...nodeLatestConfig.rules,
      ...jestPlugin.configs['flat/recommended'].rules,
      'jest/expect-expect': 'error',
      'jest/padding-around-test-blocks': 'error',
    },
  },
];
