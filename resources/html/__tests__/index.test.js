const fs = require('fs');
const path = require('path');
const vm = require('vm');

describe('index.html inline scripts', () => {
  beforeEach(() => {
    // Set up global environment
    global.document = {
      body: {
        addEventListener: jest.fn(),
      },
      getElementById: jest.fn(() => ({
        focus: jest.fn(),
      })),
    };
  });

  afterEach(() => {
    // Clean up globals
    delete global.document;
  });

  // Since the code is wrapped in an IIFE, we test observable behavior and side effects
  describe('first inline script (App Url validation)', () => {
    const APP_URL = 'http://test.app.com';

    let mainScriptContent;

    // Helper to execute the script and trigger load event
    const executeScriptAndTriggerLoad = () => {
      // Execute the IIFE script
      mainScriptContent = mainScriptContent.replace(/\$\{url\}/g, APP_URL);
      vm.runInNewContext(mainScriptContent, global);

      // Find and execute the load event handler
      const loadCall = global.window.addEventListener.mock.calls.find((call) => call[0] === 'load');
      if (loadCall && loadCall[1]) {
        loadCall[1](); // Execute the load handler
      }
    };

    beforeEach(() => {
      // Read and extract the main inline script content (IIFE)
      const htmlContent = fs.readFileSync(path.join(__dirname, '../index.html'), 'utf8');
      const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
      const scripts = Array.from(scriptMatches, (m) => m[1]);
      mainScriptContent = scripts[0] || '';

      let mockLogIdCounter = 0;

      // To get the original implementation of any utils, we must first create a mock `window` object,
      // then execute the utils script and then grab what we need from the mock `window` object.
      jest.isolateModules(() => {
        global.window = {};
        require('../../scripts/utils/url.js');
      });

      // Set up mock environment
      const mockUtils = {
        logger: {
          LOG_LEVEL: { INFO: 'info' },
          addBaseData: jest.fn(),
          append: jest.fn(),
          createId: jest.fn(() => {
            mockLogIdCounter++;
            return `mock-log-id-${mockLogIdCounter}`;
          }),
          send: jest.fn(),
        },
        url: {
          getQueryParams: jest.fn(() => ({})),
          append: jest.fn(global.window.utils.url.append),
        },
        generateUUID: jest.fn(() => 'mock-uuid'),
        request: jest.fn(),
      };

      mockUtils.request.getRequestType = jest.fn(() => 'mock-request-type');

      const mockExit = jest.fn();
      const mockTizen = {
        application: {
          getCurrentApplication: jest.fn(() => ({
            exit: mockExit,
          })),
        },
        systeminfo: {
          getCapability: jest.fn(),
        },
      };

      // Store timeout calls for testing
      const timeoutCalls = [];
      const mockWindow = {
        addEventListener: jest.fn(),
        location: { href: '' },
        performance: { now: jest.fn(() => 100) }, // For containerStartTime calculation
        setTimeout: jest.fn((fn, delay) => {
          timeoutCalls.push({ fn, delay });
          // Execute immediately for testing
          fn();
          return timeoutCalls.length;
        }),
        tizen: mockTizen,
        utils: mockUtils,
      };

      // Expose timeout calls for testing
      mockWindow.setTimeout.calls = timeoutCalls;

      // Set up global environment
      global.window = mockWindow;
      global.Date = { now: jest.fn(() => 1000) }; // For containerStartTime calculation
    });

    afterEach(() => {
      // Clean up globals
      delete global.window;
      delete global.Date;
      jest.clearAllMocks();
    });

    describe('init', () => {
      it('should execute without errors', () => {
        expect(() => {
          vm.runInNewContext(mainScriptContent, global);
        }).not.toThrow();
      });

      it('should initialize the event listeners', () => {
        executeScriptAndTriggerLoad();
        expect(global.document.body.addEventListener).toHaveBeenCalledWith(
          'keydown',
          expect.any(Function)
        );
      });

      it('should initialize the logger', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.logger.createId).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith({
          containerSession: 'mock-uuid',
          level: 'info',
          requestType: 'mock-request-type',
        });
      });

      it('should generate a UUID if containerSession is not provided in query params', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.generateUUID).toHaveBeenCalledTimes(1);
        // Assert: The new session is used for logging
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({ containerSession: 'mock-uuid' })
        );
      });

      it('should not generate a UUID if containerSession is provided in query params', () => {
        global.window.utils.url.getQueryParams.mockReturnValue({
          containerSession: 'existing-session-456',
        });

        executeScriptAndTriggerLoad();
        expect(global.window.utils.generateUUID).not.toHaveBeenCalled();
        // Assert: The existing session is used for logging
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({ containerSession: 'existing-session-456' })
        );
      });

      it('should use overallAttempts from query params if provided', () => {
        global.window.utils.url.getQueryParams.mockReturnValue({
          overallAttempts: '3',
        });

        // We only need to check the first attempt's behavior
        global.window.utils.request.mockImplementationOnce(() => {});

        executeScriptAndTriggerLoad();

        // The first call to validateAppUrl will increment the counter from 3 to 4
        expect(global.window.utils.logger.append).toHaveBeenCalledWith(
          'mock-log-id-1',
          expect.objectContaining({ step: 'attempt 4' })
        );
      });

      it('should get the OS version from tizen systeminfo and use it for logging', () => {
        global.window.tizen.systeminfo.getCapability.mockReturnValue('tizen-os-123');

        executeScriptAndTriggerLoad();

        expect(global.window.tizen.systeminfo.getCapability).toHaveBeenCalledWith(
          'http://tizen.org/feature/platform.version'
        );
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({
            osVersion: 'tizen-os-123',
          })
        );
      });

      it('should continue execution if getting osVersion from tizen fails', () => {
        global.window.tizen.systeminfo.getCapability.mockImplementation(() => {
          throw new Error('Tizen systeminfo not available');
        });

        expect(() => executeScriptAndTriggerLoad()).not.toThrow();

        // Logger should be initialized, but with osVersion as undefined
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({
            osVersion: undefined,
          })
        );
      });

      it('should send a request to validate the app url', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.request).toHaveBeenCalledTimes(1);
        expect(global.window.utils.request).toHaveBeenCalledWith({
          url: expect.stringContaining(
            'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000'
          ),
          method: 'GET',
          fail: expect.any(Function),
          success: expect.any(Function),
          timeout: 10000,
        });
      });
    });

    describe('Event Listeners', () => {
      let keydownHandler;

      beforeEach(() => {
        executeScriptAndTriggerLoad();
        // Get the keydown event handler
        const keydownCall = global.document.body.addEventListener.mock.calls.find(
          (call) => call[0] === 'keydown'
        );
        keydownHandler = keydownCall[1];
      });

      it('should exit the application on "back" key press', () => {
        keydownHandler({ keyCode: 10009 }); // Simulate "back" button
        expect(global.window.tizen.application.getCurrentApplication().exit).toHaveBeenCalledTimes(
          1
        );
      });

      it('should not exit the application on non-back keys press', () => {
        keydownHandler({ keyCode: 13 }); // Enter key
        expect(global.window.tizen.application.getCurrentApplication().exit).not.toHaveBeenCalled();
      });
    });

    describe('App URL Validation and retry flow', () => {
      it('should not log information and redirect to the app url on successful validation', () => {
        global.window.utils.request.mockImplementation(({ success }) =>
          success({ responseData: 'ok', ok: true, status: 200 })
        );

        executeScriptAndTriggerLoad();

        expect(global.window.utils.request).toHaveBeenCalledTimes(1);

        expect(global.window.utils.logger.append).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(1, 'mock-log-id-1', {
          appUrl: 'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000',
          step: 'attempt 1',
        });
        expect(global.window.utils.logger.send).not.toHaveBeenCalled();

        expect(global.window.location.href).toBe(
          'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000'
        );
      });

      it('should retry on validation failure with increasing delays when linearDelayBetweenAttempts is true', () => {
        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error', ok: false, status: 500 })
        );

        executeScriptAndTriggerLoad();
        // Since setTimeout executes immediately in our mock, all retries happen synchronously
        // First attempt + 2 retries = 3 total calls
        expect(global.window.utils.request).toHaveBeenCalledTimes(3);

        // Check that setTimeout was called with increasing delays
        const timeoutCalls = global.window.setTimeout.calls;
        expect(timeoutCalls[0].delay).toBe(2000);
      });

      it('should retry on fetch failure with a fixed delay when linearDelayBetweenAttempts is false', () => {
        // Modify the script to set linear delay to false
        mainScriptContent = mainScriptContent.replace(
          'var LINEAR_DELAY_BETWEEN_ATTEMPTS = true;',
          'var LINEAR_DELAY_BETWEEN_ATTEMPTS = false;'
        );

        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error' })
        );

        executeScriptAndTriggerLoad();
        // Since setTimeout executes immediately in our mock, all retries happen synchronously
        // First attempt + 2 retries = 3 total calls
        expect(global.window.utils.request).toHaveBeenCalledTimes(3);

        // Check that setTimeout was called with fixed delays
        const timeoutCalls = global.window.setTimeout.calls;
        expect(timeoutCalls[0].delay).toBe(2000); // First retry: fixed delay
        expect(timeoutCalls[1].delay).toBe(2000); // Second retry: fixed delay
      });

      it('should log each attempt to New Relic', () => {
        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error', ok: false, status: 500 })
        );

        executeScriptAndTriggerLoad();
        // The script will make 3 attempts (1 initial + 2 retries) because the mock setTimeout is synchronous
        expect(global.window.utils.request).toHaveBeenCalledTimes(3);

        // Verify that logger.send was called for each attempt
        expect(global.window.utils.logger.append).toHaveBeenCalledTimes(6);
        expect(global.window.utils.logger.send).toHaveBeenCalledTimes(3);

        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(1, 'mock-log-id-1', {
          appUrl: 'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000',
          step: 'attempt 1',
        });
        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(2, 'mock-log-id-1', {
          error: 'network error',
          ok: false,
          status: 500,
        });
        expect(global.window.utils.logger.send).toHaveBeenNthCalledWith(1, 'mock-log-id-1');

        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(3, 'mock-log-id-2', {
          appUrl: 'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000',
          step: 'attempt 2',
        });
        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(4, 'mock-log-id-2', {
          error: 'network error',
          ok: false,
          status: 500,
        });
        expect(global.window.utils.logger.send).toHaveBeenNthCalledWith(2, 'mock-log-id-2');

        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(5, 'mock-log-id-3', {
          appUrl: 'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000',
          step: 'attempt 3',
        });
        expect(global.window.utils.logger.append).toHaveBeenNthCalledWith(6, 'mock-log-id-3', {
          error: 'network error',
          ok: false,
          status: 500,
        });
        expect(global.window.utils.logger.send).toHaveBeenNthCalledWith(3, 'mock-log-id-3');
      });

      it('should go to error page after max retries', () => {
        global.window.utils.request.mockImplementation(({ fail }) =>
          fail({ error: 'network error' })
        );

        executeScriptAndTriggerLoad();
        expect(global.window.utils.request).toHaveBeenCalledTimes(3);

        // Check that the final setTimeout was called for error page redirect
        const timeoutCalls = global.window.setTimeout.calls;
        const lastCall = timeoutCalls[timeoutCalls.length - 1];
        expect(lastCall.delay).toBe(1000);

        expect(global.window.location.href).toBe(
          'error.html?appUrl=http%3A%2F%2Ftest.app.com%3FcontainerStartTime%3D900%26htmlLoadStartTime%3D1000&overallAttempts=3&containerSession=mock-uuid&containerStartTime=900'
        );
      });

      it('should succeed on a retry attempt after a failure', () => {
        // Fail on the first call, succeed on the second
        global.window.utils.request
          .mockImplementationOnce(({ fail }) =>
            fail({ error: 'network error', ok: false, status: 500 })
          )
          .mockImplementationOnce(({ success }) =>
            success({ responseData: 'ok', ok: true, status: 200 })
          );

        executeScriptAndTriggerLoad();

        // Check that two attempts were made
        expect(global.window.utils.request).toHaveBeenCalledTimes(2);

        // Check that only the failed attempt was logged
        expect(global.window.utils.logger.send).toHaveBeenCalledTimes(1);

        // Check that the final action was a redirect to the app url
        expect(global.window.location.href).toBe(
          'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000'
        );
      });

      it('should succeed on the third attempt after two failures', () => {
        // Fail on the first two calls, succeed on the third
        global.window.utils.request
          .mockImplementationOnce(({ fail }) =>
            fail({ error: 'network error', ok: false, status: 500 })
          )
          .mockImplementationOnce(({ fail }) =>
            fail({ error: 'network error', ok: false, status: 500 })
          )
          .mockImplementationOnce(({ success }) =>
            success({ responseData: 'ok', ok: true, status: 200 })
          );

        executeScriptAndTriggerLoad();

        // Check that three attempts were made
        expect(global.window.utils.request).toHaveBeenCalledTimes(3);

        // Check that only the failed attempts were logged
        expect(global.window.utils.logger.send).toHaveBeenCalledTimes(2);

        // Check that the final action was a redirect to the app url
        expect(global.window.location.href).toBe(
          'http://test.app.com?containerStartTime=900&htmlLoadStartTime=1000'
        );
      });
    });
  });

  describe('second inline script - (Focus script)', () => {
    it('should focus the splash screen element', () => {
      // Extract the second script (focus script)
      const htmlContent = fs.readFileSync(path.join(__dirname, '../index.html'), 'utf8');
      const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
      const scripts = Array.from(scriptMatches, (m) => m[1]);
      const focusScript = scripts[1] || '';

      const mockElement = { focus: jest.fn() };
      global.document.getElementById.mockReturnValue(mockElement);

      // Execute the focus script
      vm.runInNewContext(focusScript, global);

      expect(global.document.getElementById).toHaveBeenCalledWith('js-img');
      expect(mockElement.focus).toHaveBeenCalled();
    });
  });
});
