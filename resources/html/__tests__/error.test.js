const fs = require('fs');
const path = require('path');
const vm = require('vm');

describe('error.html inline scripts', () => {
  beforeEach(() => {
    const mockDocument = {
      body: {
        addEventListener: jest.fn(),
      },
      getElementById: jest.fn((id) => {
        // Return a stateful mock element
        if (!mockDocument.elements[id]) {
          mockDocument.elements[id] = {
            id,
            textContent: '',
            innerHTML: '',
            focus: jest.fn(),
          };
        }
        return mockDocument.elements[id];
      }),
      elements: {}, // Cache for mock elements
    };

    // Set up global environment
    global.document = mockDocument;
  });

  afterEach(() => {
    // Clean up globals
    delete global.document;
  });

  describe('first inline script (Error handling logic)', () => {
    let mainScriptContent;

    // Helper to execute the script and trigger load event
    const executeScriptAndTriggerLoad = (placeholders = {}) => {
      let scriptWithPlaceholders = mainScriptContent;
      scriptWithPlaceholders = scriptWithPlaceholders.replace(
        /\$\{version\}/g,
        placeholders.version || 'test-version'
      );
      scriptWithPlaceholders = scriptWithPlaceholders.replace(
        /\$\{localisationURL\}/g,
        placeholders.localisationURL || ''
      );
      scriptWithPlaceholders = scriptWithPlaceholders.replace(
        /\$\{labelsURL\}/g,
        placeholders.labelsURL || ''
      );

      // Execute the IIFE script
      vm.runInNewContext(scriptWithPlaceholders, global);

      // Find and execute the load event handler
      const loadCall = global.window.addEventListener.mock.calls.find((call) => call[0] === 'load');
      if (loadCall && loadCall[1]) {
        loadCall[1](); // Execute the load handler
      }
    };

    beforeEach(() => {
      // Read and extract the main inline script content (IIFE)
      const htmlContent = fs.readFileSync(path.join(__dirname, '../error.html'), 'utf8');
      const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
      const scripts = Array.from(scriptMatches, (m) => m[1]);
      mainScriptContent = scripts[0] || '';

      let mockLogIdCounter = 0;

      // Set up mock environment
      const mockUtils = {
        logger: {
          LOG_LEVEL: { INFO: 'info', ERROR: 'error' },
          addBaseData: jest.fn(),
          append: jest.fn(),
          createId: jest.fn(() => {
            mockLogIdCounter++;
            return `mock-log-id-${mockLogIdCounter}`;
          }),
          send: jest.fn(),
          logError: jest.fn(),
        },
        url: {
          getQueryParams: jest.fn(() => ({
            appUrl: 'http://the.app.com/failed',
            containerSession: 'session-123',
            containerStartTime: 1000000,
            overallAttempts: 3,
          })),
          append: jest.fn((url, params) => {
            if (!params) {
              return url;
            }
            const searchParams = new URLSearchParams(params);
            return `${url}?${searchParams.toString()}`;
          }),
          getOrigin: jest.fn((url) => new URL(url).origin),
        },
        request: jest.fn(),
      };

      mockUtils.request.getRequestType = jest.fn(() => 'mock-request-type');
      mockUtils.request.REQUEST_TYPE = { XHR: 'xhr' };

      const mockExit = jest.fn();
      const mockTizen = {
        application: {
          getCurrentApplication: jest.fn(() => ({
            exit: mockExit,
          })),
        },
        systeminfo: {
          getCapability: jest.fn(),
          getPropertyValue: jest.fn((type, success) => {
            if (type === 'NETWORK') {
              success({ networkType: 'WIFI' });
            } else if (type === 'BUILD') {
              success({ model: 'TIZEN_TV', manufacturer: 'SAMSUNG', buildVersion: '1.0' });
            }
          }),
        },
      };

      // Store timeout calls for testing
      const timeoutCalls = [];
      const mockWindow = {
        addEventListener: jest.fn(),
        location: { href: '' },
        performance: { now: jest.fn(() => 100) },
        setTimeout: jest.fn((fn, delay) => {
          timeoutCalls.push({ fn, delay });
          // Execute immediately for testing
          fn();
          return timeoutCalls.length;
        }),
        localStorage: {
          getItem: jest.fn(),
          setItem: jest.fn(),
        },
        navigator: {
          connection: {
            downlink: 10,
            effectiveType: '4g',
            rtt: 50,
            saveData: false,
            type: 'wifi',
          },
        },
        tizen: mockTizen,
        utils: mockUtils,
      };

      // Expose timeout calls for testing
      mockWindow.setTimeout.calls = timeoutCalls;

      // Set up global environment
      global.window = mockWindow;
      global.Date = { now: jest.fn(() => 1000) };
    });

    afterEach(() => {
      // Clean up globals
      delete global.window;
      delete global.Date;
    });

    describe('init', () => {
      it('should execute without errors', () => {
        expect(() => {
          vm.runInNewContext(mainScriptContent, global);
        }).not.toThrow();
      });

      it('should initialize the event listeners', () => {
        executeScriptAndTriggerLoad();
        expect(global.document.body.addEventListener).toHaveBeenCalledWith(
          'keydown',
          expect.any(Function)
        );
      });

      it('should initialize the logger', () => {
        executeScriptAndTriggerLoad();
        expect(global.window.utils.logger.createId).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith({
          containerSession: 'session-123',
          requestType: 'mock-request-type',
        });
      });

      it('should get the OS version from tizen systeminfo and use it for logging', () => {
        global.window.tizen.systeminfo.getCapability.mockReturnValue('tizen-os-456');

        executeScriptAndTriggerLoad();

        expect(global.window.tizen.systeminfo.getCapability).toHaveBeenCalledWith(
          'http://tizen.org/feature/platform.version'
        );
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({
            osVersion: 'tizen-os-456',
          })
        );
      });

      it('should continue execution if getting osVersion from tizen fails', () => {
        global.window.tizen.systeminfo.getCapability.mockImplementation(() => {
          throw new Error('Tizen systeminfo not available');
        });

        expect(() => executeScriptAndTriggerLoad()).not.toThrow();

        // Logger should be initialized, but with osVersion as undefined
        expect(global.window.utils.logger.addBaseData).toHaveBeenCalledWith(
          expect.objectContaining({
            osVersion: undefined,
          })
        );
      });

      it('should print the container info', () => {
        executeScriptAndTriggerLoad();
        expect(global.document.getElementById('version').textContent).toBe(
          'session-123 - test-version'
        );
      });

      it('should send a request to test the origin, if the requestType is not xhr', () => {
        global.window.utils.request.getRequestType.mockReturnValue('fetch');

        executeScriptAndTriggerLoad();

        expect(global.window.utils.request).toHaveBeenCalledWith(
          expect.objectContaining({
            method: 'HEAD',
            url: 'http://the.app.com',
          })
        );

        expect(global.window.utils.logger.append).toHaveBeenCalledWith(
          'mock-log-id-1',
          expect.objectContaining({ hostname: 'http://the.app.com' })
        );
      });

      it('should not send a request to test the origin, if the requestType is xhr', () => {
        global.window.utils.request.getRequestType.mockReturnValue('xhr');

        executeScriptAndTriggerLoad();

        const headCall = global.window.utils.request.mock.calls.find(
          (call) => call[0].method === 'HEAD'
        );
        expect(headCall).toBeUndefined();

        expect(global.window.utils.logger.append).not.toHaveBeenCalledWith(
          'mock-log-id-1',
          expect.objectContaining({ hostname: 'http://the.app.com' })
        );
      });
    });

    describe('Event Listeners', () => {
      let keydownHandler;

      beforeEach(() => {
        executeScriptAndTriggerLoad();
        const keydownCall = global.document.body.addEventListener.mock.calls.find(
          (call) => call[0] === 'keydown'
        );
        keydownHandler = keydownCall[1];
      });

      it('should exit the application on "back" key press', () => {
        keydownHandler({ keyCode: 10009 }); // Simulate "back" button
        expect(global.window.tizen.application.getCurrentApplication().exit).toHaveBeenCalledTimes(
          1
        );
      });

      it('should retry on "enter" key press', () => {
        keydownHandler({ keyCode: 13 }); // Simulate "enter" button
        expect(global.window.location.href).toBe(
          'index.html?containerSession=session-123&overallAttempts=3'
        );
      });
    });

    describe('Localisation Flow', () => {
      it('should fetch localisation and labels, then update the DOM', () => {
        const mockLocalisationURL = 'http://mock.localisation.com';
        const mockLabelsURL = 'http://mock.labels.com';

        global.window.utils.request.mockImplementation(({ url, success }) => {
          if (url === mockLocalisationURL) {
            success({
              ok: true,
              responseData: {
                headers: {
                  'x-skyott-language': 'en',
                  'x-skyott-territory': 'GB',
                },
              },
            });
          } else if (url === mockLabelsURL) {
            success({
              responseData: {
                'container.error.title': 'Test Error Title',
                'container.error.message': 'Test Error Message',
                'errorPrompt.button.retry': 'Test Retry',
              },
            });
          }
        });

        executeScriptAndTriggerLoad({
          localisationURL: mockLocalisationURL,
          labelsURL: mockLabelsURL,
        });

        expect(global.document.getElementById('title').innerHTML).toBe('Test Error Title');
        expect(global.document.getElementById('message').innerHTML).toBe('Test Error Message');
        expect(global.document.getElementById('retry').innerHTML).toBe('Test Retry');
        expect(global.window.localStorage.setItem).toHaveBeenCalledWith(
          'localisationLanguage',
          'en'
        );
        expect(global.window.localStorage.setItem).toHaveBeenCalledWith(
          'localisationTerritory',
          'GB'
        );
      });

      it('should use localStorage as a fallback for localisation', () => {
        const mockLocalisationURL = 'http://mock.localisation.com';
        const mockLabelsURL = 'http://mock.labels.com';

        // Mock localStorage to provide fallback values
        global.window.localStorage.getItem.mockImplementation((key) => {
          if (key === 'localisationLanguage') {
            return 'fr';
          }
          if (key === 'localisationTerritory') {
            return 'FR';
          }
          return null;
        });

        // Mock a failed localisation request, but a successful labels request
        global.window.utils.request.mockImplementation(({ url, fail, success }) => {
          if (url === mockLocalisationURL) {
            fail({ error: 'network error' });
          } else if (url === mockLabelsURL) {
            success({
              responseData: {
                'container.error.title': 'Titre Erreur',
                'container.error.message': 'Message Erreur',
                'errorPrompt.button.retry': 'Réessayer',
              },
            });
          }
        });

        executeScriptAndTriggerLoad({
          localisationURL: mockLocalisationURL,
          labelsURL: mockLabelsURL,
        });

        // Check that it fell back to localStorage
        expect(global.window.localStorage.getItem).toHaveBeenCalledWith('localisationLanguage');
        expect(global.window.localStorage.getItem).toHaveBeenCalledWith('localisationTerritory');

        // Check that labels were fetched with fallback headers
        expect(global.window.utils.request).toHaveBeenCalledWith(
          expect.objectContaining({
            url: mockLabelsURL,
            headers: expect.objectContaining({
              'X-SkyOTT-Language': 'fr',
              'X-SkyOTT-Territory': 'FR',
            }),
          })
        );

        // Check that DOM was updated
        expect(global.document.getElementById('title').innerHTML).toBe('Titre Erreur');
        expect(global.document.getElementById('message').innerHTML).toBe('Message Erreur');
        expect(global.document.getElementById('retry').innerHTML).toBe('Réessayer');
      });

      it('should log an error to New Relic if localisation fails', () => {
        const mockLocalisationURL = 'http://mock.localisation.com';
        const localisationError = {
          error: 'network timeout',
          status: 408,
          ok: false,
        };

        // Mock a failed localisation request. Other requests can be ignored.
        global.window.utils.request.mockImplementation(({ url, fail }) => {
          if (url === mockLocalisationURL) {
            fail(localisationError);
          }
        });

        executeScriptAndTriggerLoad({
          localisationURL: mockLocalisationURL,
        });

        expect(global.window.utils.logger.logError).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.logError).toHaveBeenCalledWith(
          'Error while fetching localisation',
          localisationError
        );
      });

      it('should not update the DOM, if fetching labels fails', () => {
        const mockLocalisationURL = 'http://mock.localisation.com';
        const mockLabelsURL = 'http://mock.labels.com';
        const labelsError = { error: 'server unavailable', status: 503 };

        // Mock a successful localisation request, but a failed labels request
        global.window.utils.request.mockImplementation(({ url, success, fail }) => {
          if (url === mockLocalisationURL) {
            success({
              ok: true,
              responseData: {
                headers: {
                  'x-skyott-language': 'en',
                  'x-skyott-territory': 'GB',
                },
              },
            });
          } else if (url === mockLabelsURL) {
            fail(labelsError);
          }
        });

        executeScriptAndTriggerLoad({
          localisationURL: mockLocalisationURL,
          labelsURL: mockLabelsURL,
        });

        // Check that the DOM was not updated and elements retain their initial state
        expect(global.document.getElementById('title').innerHTML).toBe('');
        expect(global.document.getElementById('message').innerHTML).toBe('');
        expect(global.document.getElementById('retry').innerHTML).toBe('');
      });

      it('should log an error if fetching labels fails', () => {
        const mockLocalisationURL = 'http://mock.localisation.com';
        const mockLabelsURL = 'http://mock.labels.com';
        const labelsError = { error: 'server unavailable', status: 503 };

        // Mock a successful localisation request, but a failed labels request
        global.window.utils.request.mockImplementation(({ url, success, fail }) => {
          if (url === mockLocalisationURL) {
            success({
              ok: true,
              responseData: {
                headers: {
                  'x-skyott-language': 'en',
                  'x-skyott-territory': 'GB',
                },
              },
            });
          } else if (url === mockLabelsURL) {
            fail(labelsError);
          }
        });

        executeScriptAndTriggerLoad({
          localisationURL: mockLocalisationURL,
          labelsURL: mockLabelsURL,
        });

        expect(global.window.utils.logger.logError).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.logError).toHaveBeenCalledWith(
          'Error while fetching labels',
          labelsError
        );
      });
    });

    describe('Logging Flow', () => {
      it('should log error details and system info', () => {
        global.window.utils.request.mockImplementation(({ method, success, fail }) => {
          if (method === 'HEAD') {
            success({ status: 200 });
          } else {
            fail({ error: 'network error' });
          }
        });

        executeScriptAndTriggerLoad();

        expect(global.window.utils.logger.append).toHaveBeenCalledTimes(6);

        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          networkType: 'WIFI',
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          model: 'TIZEN_TV',
          manufacturer: 'SAMSUNG',
          buildVersion: '1.0',
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          hostname: 'http://the.app.com',
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          hostnameStatusCode: 200,
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          bandwidth: 10,
          connectionSpeed: '4g',
          networkType: 'wifi',
          rtt: 50,
          saveData: false,
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          level: 'error',
          message: 'Container error occurred',
          step: 'show error page',
          secondsToError: expect.any(Number),
        });

        expect(global.window.utils.logger.send).toHaveBeenCalledTimes(1);
        expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-log-id-1');
      });

      it('should still send a log if tizen.systeminfo fails', () => {
        global.window.utils.request.mockImplementation(({ method, success }) => {
          if (method === 'HEAD') {
            success({ status: 200 });
          }
        });
        global.window.tizen.systeminfo.getPropertyValue.mockImplementation(
          (type, success, error) => {
            error({ name: 'TizenError', message: 'Could not get property' });
          }
        );

        executeScriptAndTriggerLoad();

        expect(global.window.utils.logger.append).toHaveBeenCalledTimes(4);

        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          hostname: 'http://the.app.com',
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          hostnameStatusCode: 200,
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          bandwidth: 10,
          connectionSpeed: '4g',
          networkType: 'wifi',
          rtt: 50,
          saveData: false,
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          level: 'error',
          message: 'Container error occurred',
          step: 'show error page',
          secondsToError: expect.any(Number),
        });

        expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-log-id-1');
      });

      it('should log an error and continue if getting network info throws an exception', () => {
        global.window.utils.request.mockImplementation(({ method, success }) => {
          if (method === 'HEAD') {
            success({ status: 200 });
          }
        });
        const networkError = new Error('Tizen API has exploded');
        global.window.tizen.systeminfo.getPropertyValue.mockImplementation((type, success) => {
          if (type === 'NETWORK') {
            throw networkError;
          } else if (type === 'BUILD') {
            success({ model: 'TIZEN_TV', manufacturer: 'SAMSUNG', buildVersion: '1.0' });
          }
        });

        executeScriptAndTriggerLoad();

        // It should log the specific error from the catch block
        expect(global.window.utils.logger.logError).toHaveBeenCalledWith(
          'Error while fetching system network info',
          networkError
        );

        // It should still have logged the build info
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          model: 'TIZEN_TV',
          manufacturer: 'SAMSUNG',
          buildVersion: '1.0',
        });

        // The final log should still be sent
        expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-log-id-1');
      });

      it('should log an error and continue if getting build info throws an exception', () => {
        global.window.utils.request.mockImplementation(({ method, success }) => {
          if (method === 'HEAD') {
            success({ status: 200 });
          }
        });
        const buildError = new Error('Tizen build info unavailable');
        global.window.tizen.systeminfo.getPropertyValue.mockImplementation((type, success) => {
          if (type === 'NETWORK') {
            success({ networkType: 'WIFI' });
          } else if (type === 'BUILD') {
            throw buildError;
          }
        });

        executeScriptAndTriggerLoad();

        // It should log the specific error from the catch block
        expect(global.window.utils.logger.logError).toHaveBeenCalledWith(
          'Error while fetching system build info',
          buildError
        );

        // It should still have logged the network info
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          networkType: 'WIFI',
        });

        // The final log should still be sent
        expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-log-id-1');
      });

      it('should still send a log if also the origin request fails', () => {
        global.window.utils.request.mockImplementation(({ method, fail }) => {
          if (method === 'HEAD') {
            fail({ statusText: 'Not Found', ok: false, status: 404 });
          }
        });
        global.window.tizen.systeminfo.getPropertyValue.mockImplementation(
          (type, success, error) => {
            error({ name: 'TizenError', message: 'Could not get property' });
          }
        );

        executeScriptAndTriggerLoad();

        expect(global.window.utils.logger.append).toHaveBeenCalledTimes(4);

        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          hostname: 'http://the.app.com',
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          hostnameStatusCode: 404,
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          bandwidth: 10,
          connectionSpeed: '4g',
          networkType: 'wifi',
          rtt: 50,
          saveData: false,
        });
        expect(global.window.utils.logger.append).toHaveBeenCalledWith('mock-log-id-1', {
          level: 'error',
          message: 'Container error occurred',
          step: 'show error page',
          secondsToError: expect.any(Number),
        });

        expect(global.window.utils.logger.send).toHaveBeenCalledWith('mock-log-id-1');
      });
    });
  });

  describe('second inline script (Focus script)', () => {
    it('should focus the wrapper element', () => {
      // Extract the second script (focus script)
      const htmlContent = fs.readFileSync(path.join(__dirname, '../error.html'), 'utf8');
      const scriptMatches = htmlContent.matchAll(/<script>([\s\S]*?)<\/script>/g);
      const scripts = Array.from(scriptMatches, (m) => m[1]);
      const focusScript = scripts[1] || '';

      const mockElement = { focus: jest.fn() };
      global.document.getElementById.mockReturnValue(mockElement);

      // Execute the focus script
      vm.runInNewContext(focusScript, global);

      expect(global.document.getElementById).toHaveBeenCalledWith('wrapper');
      expect(mockElement.focus).toHaveBeenCalled();
    });
  });
});
