<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <meta name="description" content="Peacock" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/utils/url.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/request.js"></script>
    <script>
      // Use IIFE to avoid polluting the global scope and having unexpected behaviours
      (function () {
        var LOG_ID = {};
        var errorPageDisplayTime;
        var queryParams;
        var utils;

        function defineVars() {
          utils = window.utils;
          LOG_ID.ERROR_PAGE = utils.logger.createId();
          var delta =
            window.performance && window.performance.now ? Math.round(window.performance.now()) : 0;
          errorPageDisplayTime = Date.now() - delta;
          queryParams = utils.url.getQueryParams();
        }

        function initialiseEventListeners() {
          document.body.addEventListener('keydown', function (e) {
            if (e.keyCode === 10009) {
              // back
              try {
                window.tizen.application.getCurrentApplication().exit();
              } catch (_) {
                // noop
              }
            } else if (e.keyCode === 13) {
              // enter
              window.location.href = utils.url.append('index.html', {
                containerSession: queryParams.containerSession,
                overallAttempts: queryParams.overallAttempts,
              });
            }
          });
        }

        function initialiseLogger() {
          utils.logger.addBaseData({
            containerSession: queryParams.containerSession,
            requestType: utils.request.getRequestType(),
          });
        }

        function printContainerInfo() {
          var versionElement = document.getElementById('version');
          versionElement.textContent = queryParams.containerSession + ' - ${version}';
        }

        function updateLocalisedElements(labels) {
          var title = document.getElementById('title');
          var message = document.getElementById('message');
          var retry = document.getElementById('retry');

          var errorTitle = labels['container.error.title'];
          if (errorTitle) {
            title.innerHTML = errorTitle;
          }

          var errorMessage = labels['container.error.message'];
          if (errorMessage) {
            message.innerHTML = errorMessage;
          }

          var retryTitle = labels['errorPrompt.button.retry'];
          if (retryTitle) {
            retry.innerHTML = retryTitle;
          }
        }

        function fetchLabels(params) {
          var url = '${labelsURL}';
          if (!url || url.indexOf('${') !== -1) {
            return;
          }

          return utils.request({
            headers: {
              Accept: 'application/json',
              'X-SkyOTT-ActiveTerritory': params.territory,
              'X-SkyOTT-Device': 'TV',
              'X-SkyOTT-Language': params.language,
              'X-SkyOTT-Platform': 'SAMSUNG',
              'X-SkyOTT-Proposition': '${proposition}',
              'X-SkyOTT-Provider': '${provider}',
              'X-SkyOTT-Territory': params.territory,
            },
            method: 'GET',
            url: url,
            success: params.success,
            fail: params.fail,
          });
        }

        function fetchLocalisationCallback(response) {
          var language;
          var territory;

          if (response && response.ok && response.responseData) {
            var responseData = response.responseData;
            language = responseData.headers['x-skyott-language'];
            territory = responseData.headers['x-skyott-territory'];
            window.localStorage.setItem('localisationLanguage', language);
            window.localStorage.setItem('localisationTerritory', territory);
          } else {
            language = window.localStorage.getItem('localisationLanguage');
            territory = window.localStorage.getItem('localisationTerritory');
          }

          if (!language || !territory) {
            return;
          }

          return fetchLabels({
            language: language,
            territory: territory,
            success: function (response) {
              updateLocalisedElements(response.responseData);
            },
            fail: function (error) {
              utils.logger.logError('Error while fetching labels', error);
            },
          });
        }

        function fetchLocalisation(params) {
          var headers = {
            'Content-MD5': 'd41d8cd98f00b204e9800998ecf8427e',
            'X-SkyOTT-Territory': 'undefined',
            'X-SkyOTT-Provider': '${provider}',
            Accept: 'application/vnd.localisationinfo.v1+json',
            'X-SkyOTT-Proposition': '${proposition}',
            'X-SkyOTT-Language': 'undefined',
          };

          var appNamespace = '${appNamespace}';
          if (appNamespace) {
            headers['X-SkyOTT-AppNamespace'] = appNamespace;
          }

          if ('${url}'.indexOf('forcedLocationID') > -1) {
            headers['X-SkyOTT-Debug'] = '';
            headers['X-SkyOTT-DeviceId'] = 'samsung-nl';
          }

          return utils.request({
            headers: headers,
            method: 'GET',
            url: params.url,
            success: params.success,
            fail: params.fail,
          });
        }

        function fetchLocalisationAndLabels() {
          var url = '${localisationURL}';
          if (!url || url.indexOf('${') !== -1) {
            fetchLocalisationCallback();
            return;
          }

          fetchLocalisation({
            url: url,
            success: fetchLocalisationCallback,
            fail: function (error) {
              utils.logger.logError('Error while fetching localisation', error);
              fetchLocalisationCallback();
            },
          });
        }

        function fetchSystemNetworkInfo(done) {
          function success(networkInfo) {
            utils.logger.append(LOG_ID.ERROR_PAGE, {
              networkType: networkInfo.networkType,
            });
            done();
          }
          function fail(error) {
            utils.logger.logError('Error while fetching system network info', error);
            done();
          }
          try {
            window.tizen.systeminfo.getPropertyValue('NETWORK', success, fail);
          } catch (e) {
            fail(e);
          }
        }

        function fetchSystemBuildInfo(done) {
          function success(systemInfo) {
            utils.logger.append(LOG_ID.ERROR_PAGE, {
              model: systemInfo.model,
              manufacturer: systemInfo.manufacturer,
              buildVersion: systemInfo.buildVersion,
            });
            done();
          }
          function fail(error) {
            utils.logger.logError('Error while fetching system build info', error);
            done();
          }
          try {
            window.tizen.systeminfo.getPropertyValue('BUILD', success, fail);
          } catch (e) {
            fail(e);
          }
        }

        function testHostName(done) {
          if (utils.request.getRequestType() === utils.request.REQUEST_TYPE.XHR) {
            return done();
          }

          var hostname = utils.url.getOrigin(queryParams.appUrl);

          utils.logger.append(LOG_ID.ERROR_PAGE, {
            hostname: hostname,
          });

          function testHostNameFinally(response) {
            if (response) {
              utils.logger.append(LOG_ID.ERROR_PAGE, {
                hostnameStatusCode: response.status,
              });
            }
            done();
          }

          utils.request({
            url: hostname,
            method: 'HEAD',
            success: testHostNameFinally,
            fail: testHostNameFinally,
          });
        }

        function fetchExtraInfoAndSend() {
          var jobs = [testHostName, fetchSystemBuildInfo, fetchSystemNetworkInfo];
          var counter = 0;
          function done() {
            counter++;
            if (counter === jobs.length) {
              if (utils.logger) {
                utils.logger.send(LOG_ID.ERROR_PAGE);
              }
            }
          }
          for (var i = 0; i < jobs.length; i++) {
            jobs[i](done);
          }
        }

        function logErrorToNewRelic() {
          if (window.navigator && window.navigator.connection) {
            utils.logger.append(LOG_ID.ERROR_PAGE, {
              bandwidth: window.navigator.connection.downlink,
              connectionSpeed: window.navigator.connection.effectiveType,
              networkType: window.navigator.connection.type,
              rtt: window.navigator.connection.rtt,
              saveData: window.navigator.connection.saveData,
            });
          }

          utils.logger.append(LOG_ID.ERROR_PAGE, {
            level: utils.logger.LOG_LEVEL.ERROR,
            message: 'Container error occurred',
            step: 'show error page',
            secondsToError: (errorPageDisplayTime - queryParams.containerStartTime) / 1000,
          });

          fetchExtraInfoAndSend();
        }

        window.addEventListener('load', function () {
          defineVars();
          initialiseEventListeners();
          initialiseLogger();
          printContainerInfo();
          fetchLocalisationAndLabels();
          logErrorToNewRelic();
        });
      })();
    </script>
  </head>

  <body>
    <img src="../images/error.jpeg" />
    <div id="wrapper" tabindex="-1" role="" aria-label="">
      <div id="title" class="title">${errorTitle}</div>
      <div id="message" class="message">${errorMessage}</div>
      <img class="logo" src="../images/logo.png" alt="" />
      <span class="version" id="version">${version}</span>
      <button id="retry" class="retrybutton">RETRY</button>
    </div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('wrapper').focus();
    </script>
  </body>
</html>
