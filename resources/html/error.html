<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <meta name="description" content="Peacock" />
    <link rel="stylesheet" type="text/css" href="../css/style.css" />
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/utils/url.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/request.js"></script>
    <script>
      // Use IIFE to avoid polluting the global scope and having unexpected behaviours
      (function () {
        var errorPageDisplayTime;
        var osVersion;
        var queryParams;
        var tizen;
        var utils;
        var shouldLog;

        function defineVars() {
          tizen = window.tizen;
          utils = window.utils;
          shouldLog = true;
          var delta =
            window.performance && window.performance.now ? Math.round(window.performance.now()) : 0;
          errorPageDisplayTime = Date.now() - delta;
          queryParams = utils.url.getQueryParams();
          try {
            osVersion = tizen.systeminfo.getCapability('http://tizen.org/feature/platform.version');
          } catch (_) {
            // noop
          }
        }

        function initialiseEventListeners() {
          document.body.addEventListener('keydown', function (e) {
            if (e.keyCode === 10009) {
              // back
              try {
                tizen.application.getCurrentApplication().exit();
              } catch (_) {
                // noop
              }
            } else if (e.keyCode === 13) {
              // enter
              window.location.href = utils.url.addQueryParams('index.html', {
                containerSession: queryParams.containerSession,
                overallAttempts: queryParams.overallAttempts,
              });
            }
          });
        }

        function initialiseLogger() {
          utils.logger.initialise({
            containerSession: queryParams.containerSession,
            osVersion: osVersion,
            requestType: utils.request.getRequestType(),
            shouldLog: shouldLog,
          });
        }

        function printContainerInfo() {
          var versionElement = document.getElementById('version');
          versionElement.textContent = queryParams.containerSession + ' - ${version}';
        }

        function updateLocalisedElements(labels) {
          var title = document.getElementById('title');
          var message = document.getElementById('message');
          var retry = document.getElementById('retry');

          var errorTitle = labels['container.error.title'];
          if (errorTitle) {
            title.innerHTML = errorTitle;
          }

          var errorMessage = labels['container.error.message'];
          if (errorMessage) {
            message.innerHTML = errorMessage;
          }

          var retryTitle = labels['errorPrompt.button.retry'];
          if (retryTitle) {
            retry.innerHTML = retryTitle;
          }
        }

        function fetchLabels(params) {
          var url = '${labelsURL}';
          if (!url || url.indexOf('${') !== -1) {
            return;
          }

          return utils.request({
            headers: {
              Accept: 'application/json',
              'X-SkyOTT-ActiveTerritory': params.territory,
              'X-SkyOTT-Device': 'TV',
              'X-SkyOTT-Language': params.language,
              'X-SkyOTT-Platform': 'SAMSUNG',
              'X-SkyOTT-Proposition': '${proposition}',
              'X-SkyOTT-Provider': '${provider}',
              'X-SkyOTT-Territory': params.territory,
            },
            method: 'GET',
            url: url,
            success: params.success,
            fail: params.fail,
          });
        }

        function fetchLocalisationCallback(response) {
          var language;
          var territory;

          if (response && response.ok && response.responseData) {
            var responseData = response.responseData;
            language = responseData.headers['x-skyott-language'];
            territory = responseData.headers['x-skyott-territory'];
            window.localStorage.setItem('localisationLanguage', language);
            window.localStorage.setItem('localisationTerritory', territory);
          } else {
            language = window.localStorage.getItem('localisationLanguage');
            territory = window.localStorage.getItem('localisationTerritory');
          }

          if (!language || !territory) {
            return;
          }

          return fetchLabels({
            language: language,
            territory: territory,
            success: function (response) {
              updateLocalisedElements(response.responseData);
            },
            fail: function (error) {
              utils.logger.error('Error while fetching labels', error);
            },
          });
        }

        function fetchLocalisation(params) {
          var headers = {
            'Content-MD5': 'd41d8cd98f00b204e9800998ecf8427e',
            'X-SkyOTT-Territory': 'undefined',
            'X-SkyOTT-Provider': '${provider}',
            Accept: 'application/vnd.localisationinfo.v1+json',
            'X-SkyOTT-Proposition': '${proposition}',
            'X-SkyOTT-Language': 'undefined',
          };

          var appNamespace = '${appNamespace}';
          if (appNamespace) {
            headers['X-SkyOTT-AppNamespace'] = appNamespace;
          }

          if ('${url}'.indexOf('forcedLocationID') > -1) {
            headers['X-SkyOTT-Debug'] = '';
            headers['X-SkyOTT-DeviceId'] = 'samsung-nl';
          }

          return utils.request({
            headers: headers,
            method: 'GET',
            url: params.url,
            success: params.success,
            fail: params.fail,
          });
        }

        function fetchLocalisationAndLabels() {
          var url = '${localisationURL}';
          if (!url || url.indexOf('${') !== -1) {
            fetchLocalisationCallback();
            return;
          }

          fetchLocalisation({
            url: url,
            success: fetchLocalisationCallback,
            fail: function (error) {
              utils.logger.error('Error while fetching localisation', error);
              fetchLocalisationCallback();
            },
          });
        }

        function fetchSystemNetworkInfo() {
          function success(networkInfo) {
            return {
              networkType: networkInfo.networkType,
            };
          }
          function fail(error) {
            utils.logger.error('Error while fetching system network info', error);
            return {};
          }
          try {
            tizen.systeminfo.getPropertyValue('NETWORK', success, fail);
          } catch (e) {
            fail(e);
          }
        }

        function fetchSystemBuildInfo() {
          function success(systemInfo) {
            return {
              model: systemInfo.model,
              manufacturer: systemInfo.manufacturer,
              buildVersion: systemInfo.buildVersion,
            };
          }
          function fail(error) {
            utils.logger.error('Error while fetching system build info', error);
            return {};
          }
          try {
            tizen.systeminfo.getPropertyValue('BUILD', success, fail);
          } catch (e) {
            fail(e);
          }
        }

        function testHostName() {
          if (utils.request.getRequestType() === utils.request.REQUEST_TYPE.XHR) {
            return {};
          }

          var hostname = utils.url.getOrigin(queryParams.appUrl);

          function testHostNameFinally(response) {
            return {
              hostnameStatusCode: response.status,
            };
          }

          utils.request({
            url: hostname,
            method: 'HEAD',
            success: testHostNameFinally,
            fail: testHostNameFinally,
          });
        }

        function logErrorToNewRelic() {
          var extraInfo = {};
          if (window.navigator && window.navigator.connection) {
            extraInfo = {
              bandwidth: window.navigator.connection.downlink,
              connectionSpeed: window.navigator.connection.effectiveType,
              networkType: window.navigator.connection.type,
              rtt: window.navigator.connection.rtt,
              saveData: window.navigator.connection.saveData,
            };
          }

          // eslint-disable-next-line es5/no-es6-static-methods
          Object.assign(
            extraInfo,
            {
              step: 'show error page',
              secondsToError: (errorPageDisplayTime - queryParams.containerStartTime) / 1000,
            },
            testHostName(),
            fetchSystemBuildInfo(),
            fetchSystemNetworkInfo()
          );

          utils.logger.error('Container error occurred', null, extraInfo);
        }

        window.addEventListener('load', function () {
          defineVars();
          initialiseEventListeners();
          initialiseLogger();
          printContainerInfo();
          fetchLocalisationAndLabels();
          logErrorToNewRelic();
        });
      })();
    </script>
  </head>

  <body>
    <img src="../images/error.jpeg" />
    <div id="wrapper" tabindex="-1" role="" aria-label="">
      <div id="title" class="title">${errorTitle}</div>
      <div id="message" class="message">${errorMessage}</div>
      <img class="logo" src="../images/logo.png" alt="" />
      <span class="version" id="version">${version}</span>
      <button id="retry" class="retrybutton">RETRY</button>
    </div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('wrapper').focus();
    </script>
  </body>
</html>
