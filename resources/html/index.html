<!doctype html>
<html lang="en-us">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0" />
    <script src="../scripts/polyfills/object-assign.js"></script>
    <script src="../scripts/utils/url.js"></script>
    <script src="../scripts/utils/logger_nr.js"></script>
    <script src="../scripts/utils/request.js"></script>
    <script src="../scripts/utils/uuid.js"></script>
    <script>
      // Use IIFE to avoid polluting the global scope and having unexpected behaviours
      (function () {
        var APP_URL = '${url}';
        var MAX_ATTEMPTS = 3;
        var DELAY_BETWEEN_ATTEMPTS = 2000;
        var REQUEST_TIMEOUT = 10000;

        var delta =
          window.performance && window.performance.now ? Math.round(window.performance.now()) : 0;
        var containerStartTime = Date.now() - delta;

        var attemptCounter = 0;
        var overallAttempts;
        var containerSession;
        var osVersion;
        var queryParams;
        var tizen;
        var utils;
        var shouldLog;

        function defineVars() {
          tizen = window.tizen;
          utils = window.utils;
          shouldLog = true;
          queryParams = utils.url.getQueryParams();
          containerSession = queryParams.containerSession || utils.generateUUID();
          overallAttempts = Number(queryParams.overallAttempts) || 0;
          try {
            osVersion = tizen.systeminfo.getCapability('http://tizen.org/feature/platform.version');
          } catch (_) {
            // noop
          }
        }

        function initialiseEventListeners() {
          document.body.addEventListener('keydown', function (e) {
            if (e.keyCode === 10009) {
              // back
              try {
                tizen.application.getCurrentApplication().exit();
              } catch (_) {
                // noop
              }
            }
          });
        }

        function initialiseLogger() {
          utils.logger.initialise({
            containerSession: containerSession,
            osVersion: osVersion,
            requestType: utils.request.getRequestType(),
            shouldLog: shouldLog,
          });
        }

        function goToErrorPage(appUrl) {
          // Small delay to allow the last NR request to hopefully hit the server before the location change,
          // or it might be cancelled by the browser and not logged in.
          window.setTimeout(function () {
            window.location.href = utils.url.addQueryParams('error.html', {
              appUrl: appUrl,
              overallAttempts: overallAttempts + attemptCounter,
              containerSession: containerSession,
              containerStartTime: containerStartTime,
            });
          }, 1000);
        }

        function retryOrError(appUrl) {
          if (attemptCounter >= MAX_ATTEMPTS) {
            return goToErrorPage(appUrl);
          } else {
            window.setTimeout(validateAppUrl, DELAY_BETWEEN_ATTEMPTS);
          }
        }

        function validateAppUrl() {
          attemptCounter++;

          var htmlLoadStartTime = Date.now();
          var appUrl = utils.url.addQueryParams(APP_URL, {
            containerStartTime: containerStartTime,
            htmlLoadStartTime: htmlLoadStartTime,
          });

          utils.request({
            method: 'GET',
            url: appUrl,
            timeout: REQUEST_TIMEOUT,
            success: function () {
              window.location.href = appUrl;
            },
            fail: function (response) {
              utils.logger.error('Could not load app URL', response, {
                appUrl: appUrl,
                step: 'attempt ' + (overallAttempts + attemptCounter),
                response: response,
              });
              retryOrError(appUrl);
            },
          });
        }

        window.addEventListener('load', function () {
          defineVars();
          initialiseEventListeners();
          initialiseLogger();
          validateAppUrl();
        });
      })();
    </script>
  </head>

  <body style="background-color: black; margin: 0; padding: 0">
    <div
      id="js-img"
      tabindex="-1"
      role=""
      aria-label=""
      style="position: absolute; width: 100%; height: 100%; z-index: 4; background: url(${splashScreen}) center center / ${splashZoom}% no-repeat rgb(0, 0, 0);"
    ></div>
    <script>
      // This is needed to prevent the voice guide to say "document web" when launching the app
      document.getElementById('js-img').focus();
    </script>
  </body>
</html>
