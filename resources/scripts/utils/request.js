/**
 * @fileoverview
 * This module provides utility functions for making HTTP requests using either
 * `fetch` or `XMLHttpRequest` (XHR), depending on browser/platform support.
 *
 * - If `window.fetch` is available (Samsung 2017+ / Tizen 3.0+), it uses `fetch`.
 * - Otherwise (Samsung 2016 / Tizen 2.4), it falls back to XHR.
 *
 * The correct implementation is chosen automatically based on platform support.
 *
 * For fetch-based requests, timeout handling uses AbortController when available
 * to properly cancel network requests and free up resources.
 * Falls back to setTimeout-based timeout handling on older devices.
 *
 * Usage:
 *   window.utils.request(params);
 *
 * @param {Object} params - Request configuration object:
 *   @param {string} params.url - The request URL.
 *   @param {string} params.method - HTTP method (e.g., 'GET', 'POST').
 *   @param {Object} [params.headers] - Optional. HTTP headers as key-value pairs.
 *   @param {string|Object} [params.body] - Optional. Request body (for POST/PUT).
 *   @param {number} [params.timeout] - Optional. Timeout in milliseconds. Uses AbortController when available to cancel requests.
 *   @param {boolean} [params.async] - Optional. For XHR only, whether the request is asynchronous (default: true).
 *   @param {function(Object)} [params.success] - Optional. Callback for successful requests.
 *   @param {function(Object)} [params.fail] - Optional. Callback for failed requests (response not successful, errors and timeouts)
 */

(function (utils) {
  var STATUS_SUCCESS = 2;

  var REQUEST_TYPE = {
    FETCH: 'fetch',
    XHR: 'xhr',
  };

  var requestType = window.fetch ? REQUEST_TYPE.FETCH : REQUEST_TYPE.XHR;

  function getRequestType() {
    return requestType;
  }

  function parseXhrResponse(xhr, params) {
    var contentType = xhr.getResponseHeader('Content-Type');
    var responseText = xhr.responseText;

    if (contentType && /^application\/.*json/i.test(contentType)) {
      try {
        return JSON.parse(responseText);
      } catch (error) {
        if (params.fail) {
          params.fail(error);
        }
        return null;
      }
    }
    return responseText;
  }

  function safeDestroy(xhr) {
    if (typeof xhr.destroy === 'function') {
      xhr.destroy();
    }
  }

  function calculateProgress(sizeLoaded, sizeTotal) {
    if (sizeTotal > 0) {
      var progress = Math.floor((sizeLoaded / sizeTotal) * 100);
      if (!isNaN(progress)) {
        return progress + '%';
      }
    }
    return '';
  }

  // XHR is currently used only by Samsung 2016 (Tizen 2.4)
  function useXhr(params) {
    var sizeLoaded, sizeTotal;
    var xhr = new XMLHttpRequest();
    xhr.open(params.method, params.url, params.async !== false); // default to true

    // According to the official XMLHttpRequest specification, a timeout value of 0 means there is no timeout.
    if (typeof params.timeout === 'number' && params.timeout >= 0) {
      xhr.timeout = params.timeout;
    }

    if (params.headers !== undefined) {
      for (var key in params.headers) {
        if (Object.prototype.hasOwnProperty.call(params.headers, key)) {
          xhr.setRequestHeader(key, params.headers[key]);
        }
      }
    }

    xhr.onprogress = function (event) {
      sizeLoaded = event.loaded;
      sizeTotal = event.total; // only available if server sends `Content-Length` header
    };

    xhr.onload = function () {
      var statusType = Math.floor(this.status / 100);

      if (statusType === STATUS_SUCCESS && params.success) {
        var responseData = parseXhrResponse(this, params);
        if (responseData !== null) {
          params.success({
            message: 'Request successful',
            ok: true,
            progress: calculateProgress(sizeLoaded, sizeTotal),
            responseData: responseData,
            sizeLoaded: sizeLoaded,
            sizeTotal: sizeTotal,
            status: this.status,
            statusText: this.statusText,
          });
        }
      } else if (statusType !== STATUS_SUCCESS && params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request failed',
          ok: false,
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
      }

      safeDestroy(this);
    };

    xhr.onerror = function () {
      if (params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request errored',
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
      }

      safeDestroy(this);
    };

    xhr.ontimeout = function () {
      if (params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request timed out',
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
          timeout: params.timeout,
        });
      }

      safeDestroy(this);
    };

    xhr.send(params.body);
  }

  function parseFetchResponse(response, onSuccess, onError) {
    var contentType = response.headers.get('Content-Type');

    function handleParseSuccess(parsedResponseData) {
      onSuccess({
        responseData: parsedResponseData,
        response: response,
      });
    }

    function handleParseError(error) {
      onError(error);
    }

    if (contentType && /^application\/.*json/i.test(contentType)) {
      response.json().then(handleParseSuccess, handleParseError);
    } else {
      response.text().then(handleParseSuccess, handleParseError);
    }
  }

  // Fetch is used by Samsung 2017+ (Tizen 3.0+)
  function useFetch(params) {
    var timeoutId;
    var isCompleted = false;
    var abortController;
    var supportsAbortController = typeof window.AbortController === 'function';

    // Use AbortController if available for proper request cancellation
    if (supportsAbortController) {
      abortController = new window.AbortController();
    }

    function cleanup() {
      if (timeoutId) {
        window.clearTimeout(timeoutId);
        timeoutId = null;
      }
      isCompleted = true;
    }

    function handleSuccess(result) {
      if (isCompleted) {
        return;
      }

      cleanup();

      var response = result.response;
      if (response.ok && params.success) {
        params.success({
          message: 'Request successful',
          ok: true,
          responseData: result.responseData,
          status: response.status,
          statusText: response.statusText,
        });
      } else if (!response.ok && params.fail) {
        params.fail({
          message: 'Request failed',
          ok: false,
          responseData: result.responseData,
          status: response.status,
          statusText: response.statusText,
        });
      }
    }

    function handleError(error) {
      if (isCompleted) {
        return;
      }

      cleanup();

      if (params.fail) {
        if (error.name === 'AbortError' || error.message === 'Request timed out') {
          params.fail({
            message: 'Request timed out',
            timeout: params.timeout,
          });
        } else {
          params.fail({
            errorName: error.name,
            errorStack: error.stack,
            errorMessage: error.message,
            message: 'Request errored',
          });
        }
      }
    }

    function handleFetchSuccess(response) {
      if (isCompleted) {
        return;
      }
      parseFetchResponse(response, handleSuccess, handleError);
    }

    function handleFetchError(error) {
      if (isCompleted) {
        return;
      }
      handleError(error);
    }

    function handleTimeout() {
      if (isCompleted) {
        return;
      }
      // Just abort the request. The resulting AbortError will be
      // caught by handleError and reported as a timeout.
      if (abortController) {
        abortController.abort();
      } else {
        // Fallback for environments without AbortController
        handleError(new Error('Request timed out'));
      }
    }

    if (typeof params.timeout === 'number' && params.timeout > 0) {
      timeoutId = window.setTimeout(handleTimeout, params.timeout);
    }

    var fetchOptions = {
      body: params.body,
      headers: params.headers,
      method: params.method,
    };

    // Add AbortController signal if supported
    if (supportsAbortController && abortController) {
      fetchOptions.signal = abortController.signal;
    }

    // eslint-disable-next-line no-restricted-syntax
    window.fetch(params.url, fetchOptions).then(handleFetchSuccess, handleFetchError);
  }

  utils.request = requestType === REQUEST_TYPE.FETCH ? useFetch : useXhr;
  utils.request.REQUEST_TYPE = REQUEST_TYPE;
  utils.request.getRequestType = getRequestType;
})(window.utils || (window.utils = {}));
