/**
 * @fileoverview
 * This module provides utility functions for making HTTP requests using either
 * `fetch` or `XMLHttpRequest` (XHR), depending on browser/platform support.
 *
 * - If `window.fetch` is available (Samsung 2017+ / Tizen 3.0+), it uses `fetch`.
 * - Otherwise (Samsung 2016 / Tizen 2.4), it falls back to XHR.
 *
 * The correct implementation is chosen automatically based on platform support.
 *
 * Usage:
 *   window.utils.request(params);
 *
 * @param {Object} params - Request configuration object:
 *   @param {string} params.url - The request URL.
 *   @param {string} params.method - HTTP method (e.g., 'GET', 'POST').
 *   @param {Object} [params.headers] - Optional. HTTP headers as key-value pairs.
 *   @param {string|Object} [params.body] - Optional. Request body (for POST/PUT).
 *   @param {number} [params.timeout] - Optional. Timeout in milliseconds.
 *   @param {boolean} [params.async] - Optional. For XHR only, whether the request is asynchronous (default: true).
 *   @param {function(Object)} [params.success] - Optional. Callback for successful requests.
 *   @param {function(Object)} [params.fail] - Optional. Callback for failed requests (response not successful, errors and timeouts)
 */

(function (utils) {
  var STATUS_SUCCESS = 2;

  var REQUEST_TYPE = {
    FETCH: 'fetch',
    XHR: 'xhr',
  };

  var requestType = window.fetch ? REQUEST_TYPE.FETCH : REQUEST_TYPE.XHR;

  function getRequestType() {
    return requestType;
  }

  function parseXhrResponse(xhr, params) {
    var accept = params.headers && params.headers.Accept;
    var responseText = xhr.responseText;

    if (accept && /^application\/.*json$/i.test(accept)) {
      try {
        return JSON.parse(responseText);
      } catch (error) {
        if (params.fail) {
          params.fail(error);
        }
        return null;
      }
    } else {
      return responseText;
    }
  }

  function safeDestroy(xhr) {
    if (typeof xhr.destroy === 'function') {
      xhr.destroy();
    }
  }

  function calculateProgress(sizeLoaded, sizeTotal) {
    var progress = Math.floor((sizeLoaded / sizeTotal) * 100);
    return sizeTotal && !isNaN(progress) ? progress + '%' : '';
  }

  // XHR is currently used only by Samsung 2016 (Tizen 2.4)
  function useXhr(params) {
    var sizeLoaded, sizeTotal;
    var xhr = new XMLHttpRequest();
    xhr.open(params.method, params.url, params.async !== false); // default to true
    xhr.timeout = params.timeout;

    if (params.headers !== undefined) {
      for (var key in params.headers) {
        if (Object.prototype.hasOwnProperty.call(params.headers, key)) {
          xhr.setRequestHeader(key, params.headers[key]);
        }
      }
    }

    xhr.onprogress = function (event) {
      sizeLoaded = event.loaded;
      sizeTotal = event.total; // only available if server sends `Content-Length` header
    };

    xhr.onload = function () {
      var statusType = Math.floor(this.status / 100);

      if (statusType === STATUS_SUCCESS && params.success) {
        var responseData = parseXhrResponse(this, params);
        if (responseData !== null) {
          params.success({
            message: 'Request successful',
            ok: true,
            progress: calculateProgress(sizeLoaded, sizeTotal),
            responseData: responseData,
            sizeLoaded: sizeLoaded,
            sizeTotal: sizeTotal,
            status: this.status,
            statusText: this.statusText,
          });
        }
      } else if (statusType !== STATUS_SUCCESS && params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request failed',
          ok: false,
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
      }

      safeDestroy(this);
    };

    xhr.onerror = function () {
      if (params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request errored',
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
        });
      }

      safeDestroy(this);
    };

    xhr.ontimeout = function () {
      if (params.fail) {
        params.fail({
          errorCode: this.errorCode,
          errorString: this.errorString,
          message: 'Request timed out',
          progress: calculateProgress(sizeLoaded, sizeTotal),
          responseText: this.responseText,
          sizeLoaded: sizeLoaded,
          sizeTotal: sizeTotal,
          status: this.status,
          statusText: this.statusText,
          timeout: params.timeout,
        });
      }

      safeDestroy(this);
    };

    xhr.send(params.body);
  }

  function parseFetchResponse(params, response, onSuccess, onError) {
    var accept = params.headers && params.headers.Accept;

    function handleParseSuccess(parsedResponseData) {
      onSuccess({
        responseData: parsedResponseData,
        response: response,
      });
    }

    function handleParseError(error) {
      onError(error);
    }

    if (accept && /^application\/.*json$/i.test(accept)) {
      response.json().then(handleParseSuccess, handleParseError);
    } else {
      response.text().then(handleParseSuccess, handleParseError);
    }
  }

  // Fetch is used by Samsung 2017+ (Tizen 3.0+)
  function useFetch(params) {
    var timeoutId;
    var isCompleted = false;

    function cleanup() {
      if (timeoutId) {
        window.clearTimeout(timeoutId);
        timeoutId = null;
      }
      isCompleted = true;
    }

    function handleSuccess(result) {
      if (isCompleted) {
        return;
      }
      cleanup();

      var response = result.response;
      if (response.ok && params.success) {
        params.success({
          message: 'Request successful',
          ok: true,
          responseData: result.responseData,
          status: response.status,
          statusText: response.statusText,
        });
      } else if (!response.ok && params.fail) {
        params.fail({
          message: 'Request failed',
          ok: false,
          responseData: result.responseData,
          status: response.status,
          statusText: response.statusText,
        });
      }
    }

    function handleError(error) {
      if (isCompleted) {
        return;
      }
      cleanup();

      if (params.fail) {
        if (error.message === 'Request timed out') {
          params.fail({
            message: 'Request timed out',
            timeout: params.timeout,
          });
        } else {
          params.fail({
            errorName: error.name,
            errorStack: error.stack,
            errorMessage: error.message,
            message: 'Request errored',
          });
        }
      }
    }

    function handleFetchSuccess(response) {
      if (isCompleted) {
        return;
      }
      parseFetchResponse(params, response, handleSuccess, handleError);
    }

    function handleFetchError(error) {
      if (isCompleted) {
        return;
      }
      handleError(error);
    }

    function handleTimeout() {
      if (isCompleted) {
        return;
      }
      handleError(new Error('Request timed out'));
    }

    if (params.timeout !== undefined) {
      timeoutId = window.setTimeout(handleTimeout, params.timeout);
    }

    // eslint-disable-next-line no-restricted-syntax
    window
      .fetch(params.url, {
        body: params.body,
        headers: params.headers,
        method: params.method,
      })
      .then(handleFetchSuccess, handleFetchError);
  }

  utils.request = requestType === REQUEST_TYPE.FETCH ? useFetch : useXhr;
  utils.request.REQUEST_TYPE = REQUEST_TYPE;
  utils.request.getRequestType = getRequestType;
})(window.utils || (window.utils = {}));
