/**
 * @fileoverview
 * This module provides a logger utility for sending application logs to New Relic.
 *
 * Key functionalities include:
 * - Automatically including base data with each log.
 * - Providing a dedicated `logError` function for capturing and sending JavaScript errors.
 * - Sending log data to the New Relic
 *
 * Important:
 * - Ensure to use the provided polyfill for Object.assign for compatibility with Samsung 2016.
 */

(function (utils) {
  var NR_URL = 'https://log-api.newrelic.com/log/v1';
  var NR_KEY = '${newRelicApiKey}';

  var LOG_LEVEL = {
    INFO: 'info',
    ERROR: 'error',
  };

  var baseData = {};
  var shouldLog = false;

  function initialise(params) {
    shouldLog = params.shouldLog;
    baseData = {
      containerVersion: '${version}',
      logtype: 'container',
      osName: 'Tizen',
      osVersion: params.osVersion,
      platform: 'SAMSUNG',
      proposition: '${proposition}',
      provider: '${provider}',
      territory: '${territory}',
      containerSession: params.containerSession,
    };
  }

  function sendRequest(body) {
    // Do not send a request if the key is missing.
    if (!NR_KEY) {
      // eslint-disable-next-line no-console
      console.warn('New Relic API key is missing. Logs will not be sent.');
      return;
    }

    //Log sampling
    if (!shouldLog) {
      return;
    }

    utils.request({
      url: NR_URL,
      method: 'POST',
      headers: {
        'Api-Key': NR_KEY,
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      body: JSON.stringify(body),
    });
  }

  function error(message, error, extraInfo) {
    var errorInfo;

    if (error) {
      errorInfo = {
        errorMessage: error.message,
        errorName: error.name,
        errorStack: error.stack,
      };
    }

    // eslint-disable-next-line es5/no-es6-static-methods
    var body = Object.assign(
      {
        level: LOG_LEVEL.ERROR,
        message: message,
      },
      baseData,
      errorInfo,
      extraInfo
    );
    sendRequest(body);
  }

  function info(extraInfo) {
    // eslint-disable-next-line es5/no-es6-static-methods
    var body = Object.assign({}, baseData, extraInfo);
    sendRequest(body);
  }

  utils.logger = {
    initialise: initialise,
    info: info,
    error: error,
  };
})(window.utils || (window.utils = {}));
