describe('url', () => {
  beforeEach(() => {
    // Set up a mock window object for the browser environment
    global.window = {
      utils: {},
      // Mock location for getQueryParams and getOrigin
      location: {
        search: '',
        protocol: 'http:',
      },
    };
    // The script is an IIFE that modifies window.utils, so we need to load it for each test
    // after the window object is set up.
    jest.isolateModules(() => {
      require('../url.js');
    });
  });

  afterEach(() => {
    // Clean up the global window object
    delete global.window;
  });

  it('should attach url object to window.utils', () => {
    expect(global.window.utils.url).toBeInstanceOf(Object);
    expect(global.window.utils.url.addQueryParams).toBeInstanceOf(Function);
    expect(global.window.utils.url.getQueryParams).toBeInstanceOf(Function);
    expect(global.window.utils.url.getOrigin).toBeInstanceOf(Function);
  });

  describe('append', () => {
    it('should append query string to a url without params', () => {
      const url = 'http://test.com';
      const params = { a: 1, b: 'test' };
      const newUrl = global.window.utils.url.addQueryParams(url, params);
      expect(newUrl).toBe('http://test.com?a=1&b=test');
    });

    it('should append query string to a url with existing params', () => {
      const url = 'http://test.com?c=3';
      const params = { a: 1, b: 'test' };
      const newUrl = global.window.utils.url.addQueryParams(url, params);
      expect(newUrl).toBe('http://test.com?c=3&a=1&b=test');
    });

    it('should return original url if params object is empty', () => {
      const url = 'http://test.com';
      const newUrl = global.window.utils.url.addQueryParams(url, {});
      expect(newUrl).toBe('http://test.com');
    });

    it('should encode special characters in params', () => {
      const url = 'http://test.com';
      const params = { q: 'a&b=c', redirect_uri: 'http://example.com' };
      const newUrl = global.window.utils.url.addQueryParams(url, params);
      expect(newUrl).toBe('http://test.com?q=a%26b%3Dc&redirect_uri=http%3A%2F%2Fexample.com');
    });

    it('should handle url ending with a question mark', () => {
      const url = 'http://test.com?';
      const params = { a: 1 };
      const newUrl = global.window.utils.url.addQueryParams(url, params);
      expect(newUrl).toBe('http://test.com?a=1');
    });
  });

  describe('getQueryParams', () => {
    it('should parse a simple query string', () => {
      global.window.location.search = '?a=1';
      const params = global.window.utils.url.getQueryParams();
      expect(params).toEqual({ a: '1' });
    });

    it('should parse multiple query params', () => {
      global.window.location.search = '?a=1&b=hello&c=true';
      const params = global.window.utils.url.getQueryParams();
      expect(params).toEqual({ a: '1', b: 'hello', c: 'true' });
    });

    it('should return an empty object for an empty query string', () => {
      global.window.location.search = '';
      const params = global.window.utils.url.getQueryParams();
      expect(params).toEqual({});
    });

    it('should handle encoded characters', () => {
      global.window.location.search = '?q=a%20b&redirect=http%3A%2F%2Ftest.com';
      const params = global.window.utils.url.getQueryParams();
      expect(params).toEqual({ q: 'a b', redirect: 'http://test.com' });
    });
  });

  describe('getOrigin', () => {
    it('should extract origin from a standard http url', () => {
      const url = 'http://example.com/path/to/page';
      const origin = global.window.utils.url.getOrigin(url);
      expect(origin).toBe('http://example.com');
    });

    it('should extract origin from an https url with a port', () => {
      const url = 'https://example.com:8080/path?query=1';
      const origin = global.window.utils.url.getOrigin(url);
      expect(origin).toBe('https://example.com:8080');
    });

    it('should use window.location.protocol for protocol-relative urls', () => {
      global.window.location.protocol = 'https:';
      const url = '//example.com/path';
      const origin = global.window.utils.url.getOrigin(url);
      expect(origin).toBe('https://example.com');
    });

    it('should return an empty string for an invalid url', () => {
      const url = 'not a url';
      const origin = global.window.utils.url.getOrigin(url);
      expect(origin).toBe('');
    });
  });
});
