describe('request utility', () => {
  let mockFetch;
  let mockXHR;
  let mockLogger;
  let requestUtility; // The module under test

  // Helper to load the fetch utility in an isolated environment
  function loadFetchUtility(useNativeFetch = true) {
    jest.isolateModules(() => {
      // Set up global mocks before requiring the module
      global.window = {
        fetch: useNativeFetch ? mockFetch : undefined, // Control window.fetch availability
        XMLHttpRequest: mockXHR,
        utils: {
          logger: mockLogger,
        },
        // Ensure Promise is available for useFetch timeout logic
        Promise: global.Promise, // Use native Promise for testing fetch path
        // Add setTimeout and clearTimeout for timeout logic in useFetch
        setTimeout: global.setTimeout,
        clearTimeout: global.clearTimeout,
      };

      // Make XMLHttpRequest available in the global scope for the script to use
      global.XMLHttpRequest = mockXHR;

      // Require the module
      require('../request.js');
      requestUtility = global.window.utils.request;
    });
  }

  beforeEach(() => {
    // Reset mocks before each test
    // Mock fetch to return an object with then method (Promise-like but not a Promise)
    mockFetch = jest.fn(() => ({
      then: jest.fn((onSuccess, _) => {
        // Default successful response
        const mockResponse = {
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: {
            get: jest.fn().mockReturnValue('application/json'), // Default to JSON
          },
          json: () => ({
            then: (resolve) => resolve({ data: 'test' }),
          }),
          text: () => ({
            then: (resolve) => resolve('test text'),
          }),
        };
        onSuccess(mockResponse);
      }),
    }));

    mockXHR = jest.fn(() => ({
      open: jest.fn(),
      send: jest.fn(),
      setRequestHeader: jest.fn(),
      onload: null,
      getResponseHeader: jest.fn(),
      onerror: null,
      ontimeout: null,
      onprogress: null,
      timeout: 0,
      status: 200,
      responseText: '',
      errorCode: 0,
      errorString: '',
      loaded: 0,
      total: 0,
      destroy: jest.fn(),
    }));
    mockLogger = {
      logError: jest.fn(),
    };

    // Ensure real timers are used for async operations like timeouts
    jest.useRealTimers();
  });

  afterEach(() => {
    delete global.window;
    delete global.XMLHttpRequest;
    jest.restoreAllMocks();
  });

  describe('request type detection', () => {
    it('should use fetch if window.fetch is available', () => {
      loadFetchUtility(true);
      expect(requestUtility.getRequestType()).toBe('fetch');
    });

    it('should use xhr if window.fetch is not available', () => {
      loadFetchUtility(false);
      expect(requestUtility.getRequestType()).toBe('xhr');
    });
  });

  describe('useXhr', () => {
    beforeEach(() => {
      loadFetchUtility(false); // Force XHR path
    });

    it('should make a GET request and call success on 2xx status', async () => {
      const mockSuccess = jest.fn(async (response) => {
        expect(response.message).toBe('Request successful');
        expect(response.ok).toBe(true);
        expect(response.status).toBe(200);
        expect(response.responseData).toBe('mock response');
      });
      const mockFail = jest.fn();

      requestUtility({
        url: '/api/data',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      // Simulate XHR success
      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.responseText = 'mock response';
      xhrInstance.onload();

      expect(xhrInstance.open).toHaveBeenCalledWith('GET', '/api/data', true);
      expect(xhrInstance.send).toHaveBeenCalledWith(undefined);
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(mockFail).not.toHaveBeenCalled();
      expect(xhrInstance.destroy).toHaveBeenCalledTimes(1);
    });

    it('should make a POST request with body and headers', async () => {
      const mockSuccess = jest.fn((response) => {
        expect(response.status).toBe(200);
      });
      const mockFail = jest.fn(); // Not expected to be called
      const mockBody = JSON.stringify({ key: 'value' });
      const mockHeaders = { 'Content-Type': 'application/json', 'X-Custom': 'header' };

      requestUtility({
        url: '/api/post',
        method: 'POST',
        headers: mockHeaders,
        body: mockBody,
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.onload();

      expect(xhrInstance.open).toHaveBeenCalledWith('POST', '/api/post', true);
      expect(xhrInstance.setRequestHeader).toHaveBeenCalledWith('Content-Type', 'application/json');
      expect(xhrInstance.setRequestHeader).toHaveBeenCalledWith('X-Custom', 'header');
      expect(xhrInstance.send).toHaveBeenCalledWith(mockBody);
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(mockFail).not.toHaveBeenCalled();
    });

    it('should call fail on non-2xx status', async () => {
      const mockSuccess = jest.fn();
      const mockFail = jest.fn((response) => {
        expect(response.message).toBe('Request failed');
        expect(response.ok).toBe(false);
        expect(response.status).toBe(404);
        expect(response.responseText).toBe('Not Found');
      });

      requestUtility({
        url: '/api/404',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 404;
      xhrInstance.statusText = 'Not Found';
      xhrInstance.responseText = 'Not Found';
      xhrInstance.onload();

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
    });

    it('should call fail on network error', async () => {
      const mockSuccess = jest.fn(); // Not expected to be called
      const mockFail = jest.fn((response) => {
        expect(response.message).toBe('Request errored');
        expect(response.status).toBe(0); // XHR network errors often have status 0
      });

      requestUtility({
        url: '/api/error',
        method: 'GET',
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      // When a real XHR request fails due to a network error, its status is 0.
      xhrInstance.status = 0;
      xhrInstance.onerror(); // Simulate network error

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
    });

    it('should call fail on timeout', async () => {
      const mockSuccess = jest.fn();
      const mockFail = jest.fn((response) => {
        expect(response.message).toBe('Request timed out');
        expect(response.timeout).toBe(5000);
        expect(response.status).toBe(0); // Status is 0 on timeout
      });

      requestUtility({
        url: '/api/timeout',
        method: 'GET',
        timeout: 5000,
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      expect(xhrInstance.timeout).toBe(5000);

      // When a real XHR request times out, its status is 0.
      xhrInstance.status = 0;
      xhrInstance.ontimeout(); // Simulate timeout

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
    });

    it('should set xhr.timeout when timeout is 0', () => {
      requestUtility({
        url: '/api/data',
        method: 'GET',
        timeout: 0,
      });
      const xhrInstance = mockXHR.mock.results[0].value;
      expect(xhrInstance.timeout).toBe(0);
    });

    it.each([
      ['a negative number', -100],
      ['a non-numeric string', 'abc'],
      ['null', null],
      [undefined, undefined],
    ])(
      'should ignore an invalid timeout of %s and retain the default value',
      (_, invalidTimeout) => {
        requestUtility({
          url: '/api/data',
          method: 'GET',
          timeout: invalidTimeout,
        });
        const xhrInstance = mockXHR.mock.results[0].value;
        expect(xhrInstance.timeout).toBe(0); // According to the official XMLHttpRequest specification, a timeout value of 0 means there is no timeout.
      }
    );

    it('should parse JSON response if Content-Type header is application/json', async () => {
      const mockData = { data: 'json' };
      const mockSuccess = jest.fn((response) => {
        expect(response.responseData).toStrictEqual(mockData);
      });

      requestUtility({
        url: '/api/json',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.getResponseHeader.mockReturnValue('application/json');
      xhrInstance.status = 200;
      xhrInstance.responseText = JSON.stringify(mockData);
      xhrInstance.onload();
    });

    it('should call fail if JSON parsing fails for XHR', () => {
      const mockSuccess = jest.fn();
      const mockFail = jest.fn();

      requestUtility({
        url: '/api/invalid-json',
        method: 'GET',
        headers: { Accept: 'application/json' },
        success: mockSuccess,
        fail: mockFail,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.getResponseHeader.mockReturnValue('application/json');
      xhrInstance.status = 200;
      xhrInstance.responseText = 'this is not json';
      xhrInstance.onload();

      expect(mockSuccess).not.toHaveBeenCalled();
      expect(mockFail).toHaveBeenCalledTimes(1);
      expect(mockFail).toHaveBeenCalledWith(expect.any(SyntaxError));
    });

    it('should not parse JSON if Content-Type header is not application/json', async () => {
      const mockText = 'plain text response';
      const mockSuccess = jest.fn((response) => {
        expect(response.responseData).toBe(mockText);
      });

      requestUtility({
        url: '/api/text',
        method: 'GET',
        headers: { Accept: 'text/plain' },
        success: mockSuccess,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.getResponseHeader.mockReturnValue('text/plain');
      xhrInstance.status = 200;
      xhrInstance.responseText = mockText;
      xhrInstance.onload();
    });

    it('should respect async: false for XHR', () => {
      requestUtility({
        url: '/api/sync',
        method: 'GET',
        async: false,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      expect(xhrInstance.open).toHaveBeenCalledWith('GET', '/api/sync', false);
    });

    it('should call destroy on XHR instance after completion', async () => {
      const mockSuccess = jest.fn();
      requestUtility({
        url: '/api/destroy',
        method: 'GET',
        success: mockSuccess,
      });

      const xhrInstance = mockXHR.mock.results[0].value;
      xhrInstance.status = 200;
      xhrInstance.onload(); // This synchronously calls the success callback and then destroy()

      // Assert after the onload handler has completed
      expect(mockSuccess).toHaveBeenCalledTimes(1);
      expect(xhrInstance.destroy).toHaveBeenCalledTimes(1);
    });
  });

  describe('useFetch', () => {
    beforeEach(() => {
      loadFetchUtility(true); // Force Fetch path
    });

    it('should make a GET request and call success on ok response', () => {
      return new Promise((resolve) => {
        const mockResponse = {
          ok: true,
          status: 200,
          statusText: 'OK',
          headers: {
            get: jest.fn().mockReturnValue('application/json'),
          },
          json: () => ({ then: (resolve) => resolve({ data: 'fetch' }) }),
          text: () => ({ then: (resolve) => resolve('fetch') }),
        };
        mockFetch.mockReturnValue({ then: (resolve) => resolve(mockResponse) });

        const mockSuccess = jest.fn((result) => {
          expect(result).toEqual(
            expect.objectContaining({
              message: 'Request successful',
              ok: true,
              status: 200,
              responseData: { data: 'fetch' },
            })
          );
          resolve();
        });

        requestUtility({
          url: '/api/fetch-data',
          method: 'GET',
          headers: { Accept: 'application/json' },
          success: mockSuccess,
          fail: jest.fn(),
        });
      });
    });

    it('should call fail on non-ok response', () => {
      return new Promise((resolve) => {
        // Mock fetch to return a non-ok response
        mockFetch.mockReturnValue({
          then: (onSuccess) => {
            const mockResponse = {
              ok: false,
              status: 404,
              statusText: 'Not Found',
              headers: {
                get: jest.fn().mockReturnValue('application/json'),
              },
              json: () => ({
                then: (resolve) => resolve({ error: 'Not Found' }),
              }),
            };
            onSuccess(mockResponse);
          },
        });

        const mockSuccess = jest.fn();
        const mockFail = jest.fn((result) => {
          expect(mockSuccess).not.toHaveBeenCalled();
          expect(result).toEqual(
            expect.objectContaining({
              message: 'Request failed',
              ok: false,
              status: 404,
              responseData: { error: 'Not Found' },
            })
          );
          resolve();
        });

        requestUtility({
          url: '/api/fetch-404',
          method: 'GET',
          headers: { Accept: 'application/json' },
          success: mockSuccess,
          fail: mockFail,
        });
      });
    });

    it('should make a POST request with body and headers', () => {
      return new Promise((resolve) => {
        const mockBody = JSON.stringify({ item: 'new' });
        const mockHeaders = { 'Content-Type': 'application/json' };
        const url = '/api/fetch-post';

        const mockSuccess = jest.fn((result) => {
          expect(result).toEqual(
            expect.objectContaining({
              message: 'Request successful',
              ok: true,
              status: 200,
            })
          );
          resolve();
        });

        requestUtility({
          url: url,
          method: 'POST',
          headers: mockHeaders,
          body: mockBody,
          success: mockSuccess,
        });

        expect(mockFetch).toHaveBeenCalledTimes(1);
        expect(mockFetch).toHaveBeenCalledWith(url, {
          method: 'POST',
          headers: mockHeaders,
          body: mockBody,
        });
      });
    });

    it('should call fail on network error', () => {
      return new Promise((resolve) => {
        // Mock fetch to simulate network error
        mockFetch.mockReturnValue({
          then: (_, onError) => {
            const networkError = new Error('Network down');
            onError(networkError);
          },
        });

        const mockSuccess = jest.fn();
        const mockFail = jest.fn((result) => {
          expect(mockSuccess).not.toHaveBeenCalled();
          expect(result).toEqual(
            expect.objectContaining({
              message: 'Request errored',
              errorMessage: 'Network down',
            })
          );
          resolve();
        });

        requestUtility({
          url: '/api/fetch-network-error',
          method: 'GET',
          success: mockSuccess,
          fail: mockFail,
        });
      });
    });

    it('should call fail on timeout', () => {
      return new Promise((resolve) => {
        // Mock fetch to never resolve (simulate hanging request)
        mockFetch.mockReturnValue({
          then: () => {
            // Never call onSuccess or onError - simulate hanging request
          },
        });

        const mockFail = jest.fn((result) => {
          expect(result).toEqual(
            expect.objectContaining({
              message: 'Request timed out',
              timeout: 100,
            })
          );
          resolve();
        });

        requestUtility({
          url: '/api/fetch-timeout',
          method: 'GET',
          timeout: 100, // Short timeout for test
          success: jest.fn(),
          fail: mockFail,
        });
      });
    });

    describe('timeout parameter handling', () => {
      let setTimeoutSpy;

      beforeEach(() => {
        // Spy on the real setTimeout and provide a mock implementation
        // to prevent actual timers from being scheduled during the test.
        setTimeoutSpy = jest.spyOn(global, 'setTimeout').mockImplementation(() => {});
        // Reload the utility to ensure it uses our spied-on version of setTimeout.
        loadFetchUtility(true);
      });

      it.each([
        ['a negative number', -100],
        ['a non-numeric string', 'abc'],
        ['null', null],
        [undefined, undefined],
        ['zero', 0],
      ])('should not set a timeout when timeout is %s', (_, invalidTimeout) => {
        requestUtility({ url: '/api/data', method: 'GET', timeout: invalidTimeout });
        expect(setTimeoutSpy).not.toHaveBeenCalled();
      });
    });

    it('should parse JSON response if Content-Type header is application/json', () => {
      return new Promise((resolve) => {
        const mockData = { data: 'fetch json' };

        // Mock fetch to return a response with JSON data
        mockFetch.mockReturnValue({
          then: (onSuccess) => {
            const mockResponse = {
              ok: true,
              status: 200,
              statusText: 'OK',
              headers: {
                get: jest.fn().mockReturnValue('application/json'),
              },
              json: () => ({
                then: (resolve) => resolve(mockData),
              }),
            };
            onSuccess(mockResponse);
          },
        });

        const mockSuccess = jest.fn((result) => {
          expect(result).toEqual(
            expect.objectContaining({
              responseData: mockData,
            })
          );
          resolve();
        });

        requestUtility({
          url: '/api/fetch-json',
          method: 'GET',
          headers: { Accept: 'application/json' },
          success: mockSuccess,
        });
      });
    });

    it('should call fail if JSON parsing fails for Fetch', () => {
      return new Promise((resolve) => {
        const parsingError = new SyntaxError('Invalid JSON');

        // Mock fetch to return a response where JSON parsing fails
        mockFetch.mockReturnValue({
          then: (onSuccess) => {
            const mockResponse = {
              ok: true,
              status: 200,
              statusText: 'OK',
              headers: {
                get: jest.fn().mockReturnValue('application/json'),
              },
              json: () => ({
                then: (_, onError) => onError(parsingError),
              }),
            };
            onSuccess(mockResponse);
          },
        });

        const mockFail = jest.fn((result) => {
          expect(result).toEqual({
            errorName: 'SyntaxError',
            errorStack: parsingError.stack,
            errorMessage: 'Invalid JSON',
            message: 'Request errored',
          });
          resolve();
        });

        requestUtility({
          url: '/api/fetch-invalid-json',
          method: 'GET',
          headers: { Accept: 'application/json' },
          success: jest.fn(),
          fail: mockFail,
        });
      });
    });

    it('should not parse JSON if Content-Type header is not application/json', () => {
      return new Promise((resolve) => {
        const mockText = 'plain text fetch response';

        // Mock fetch to return a text response
        mockFetch.mockReturnValue({
          then: (onSuccess) => {
            const mockResponse = {
              ok: true,
              status: 200,
              statusText: 'OK',
              headers: {
                get: jest.fn().mockReturnValue('text/plain'),
              },
              text: () => ({
                then: (resolve) => resolve(mockText),
              }),
            };
            onSuccess(mockResponse);
          },
        });

        const mockSuccess = jest.fn((result) => {
          expect(result).toEqual(
            expect.objectContaining({
              responseData: mockText,
            })
          );
          resolve();
        });

        requestUtility({
          url: '/api/fetch-text',
          method: 'GET',
          headers: { Accept: 'text/plain' },
          success: mockSuccess,
        });
      });
    });

    describe('AbortController support', () => {
      let originalAbortController;
      let mockAbort;
      let mockSignal;

      beforeEach(() => {
        // Save and mock AbortController
        originalAbortController = global.window.AbortController;
        mockAbort = jest.fn();

        // Use an EventEmitter for the signal to allow for 'abort' events.
        const EventEmitter = require('events');
        mockSignal = new EventEmitter();
        mockSignal.aborted = false; // Add property for compatibility

        global.window.AbortController = jest.fn(() => ({
          abort: () => {
            mockAbort();
            mockSignal.emit('abort');
          },
          signal: mockSignal,
        }));
        // Use fake timers to control timeout behavior precisely
        jest.useFakeTimers();
      });

      afterEach(() => {
        // Restore AbortController
        if (global.window) {
          global.window.AbortController = originalAbortController;
        }
        jest.useRealTimers();
      });

      it('should use AbortController and abort on timeout', async () => {
        // Create a promise that will be resolved when the fail callback is called
        let failCallbackCalled;
        const failPromise = new Promise((resolve) => {
          failCallbackCalled = resolve;
        });

        // Mock fetch to return a promise that rejects when our mock signal aborts.
        // This simulates how a real fetch would behave when its signal is aborted.
        mockFetch.mockReturnValue(
          new Promise(function (_, reject) {
            mockSignal.on('abort', function () {
              const error = new Error('The operation was aborted.');
              error.name = 'AbortError';
              reject(error);
            });
          })
        );

        const mockFail = jest.fn(() => {
          failCallbackCalled();
        });
        requestUtility({
          url: '/api/abort-timeout',
          method: 'GET',
          timeout: 100,
          success: jest.fn(),
          fail: mockFail,
        });

        expect(global.window.AbortController).toHaveBeenCalledTimes(1);
        expect(mockAbort).not.toHaveBeenCalled();
        expect(mockFetch).toHaveBeenCalledWith(
          '/api/abort-timeout',
          expect.objectContaining({ signal: expect.any(Object) })
        );

        jest.runAllTimers();

        // Wait for the fail callback to be invoked
        await failPromise;

        expect(mockAbort).toHaveBeenCalledTimes(1);
        expect(mockFail).toHaveBeenCalledTimes(1);
        expect(mockFail).toHaveBeenCalledWith(
          expect.objectContaining({ message: 'Request timed out' })
        );
      });

      it('should not abort if the request succeeds before timeout', async () => {
        // Create a promise that will be resolved when the success callback is called
        let successCallbackCalled;
        const successPromise = new Promise((resolve) => {
          successCallbackCalled = resolve;
        });

        // Mock fetch to resolve successfully
        const mockResponse = {
          ok: true,
          status: 200,
          headers: { get: () => 'text/plain' },
          text: () => Promise.resolve('ok'),
        };
        mockFetch.mockReturnValue(Promise.resolve(mockResponse));

        const mockSuccess = jest.fn(() => {
          successCallbackCalled();
        });
        const mockFail = jest.fn();

        requestUtility({
          url: '/api/abort-success',
          method: 'GET',
          timeout: 100,
          success: mockSuccess,
          fail: mockFail,
        });

        // Wait for the success callback to be invoked
        await successPromise;

        expect(mockSuccess).toHaveBeenCalledTimes(1);
        expect(mockFail).not.toHaveBeenCalled();

        // Fast-forward time to ensure the timeout does not fire
        jest.runAllTimers();

        // The abort function should not have been called because the request completed
        expect(mockAbort).not.toHaveBeenCalled();
      });

      it('should not abort if the request fails before timeout', async () => {
        // Create a promise that will be resolved when the fail callback is called
        let failCallbackCalled;
        const failPromise = new Promise((resolve) => {
          failCallbackCalled = resolve;
        });

        // Mock fetch to reject with a network error
        const networkError = new Error('Network Error');
        mockFetch.mockReturnValue(Promise.reject(networkError));

        const mockSuccess = jest.fn();
        const mockFail = jest.fn(() => {
          failCallbackCalled();
        });

        requestUtility({
          url: '/api/abort-fail',
          method: 'GET',
          timeout: 100,
          success: mockSuccess,
          fail: mockFail,
        });

        // Wait for the fail callback to be invoked
        await failPromise;

        expect(mockSuccess).not.toHaveBeenCalled();
        expect(mockFail).toHaveBeenCalledTimes(1);
        expect(mockFail).toHaveBeenCalledWith(
          expect.objectContaining({ message: 'Request errored' })
        );

        // Fast-forward time to ensure the timeout does not fire
        jest.runAllTimers();

        // The abort function should not have been called because the request completed (by failing)
        expect(mockAbort).not.toHaveBeenCalled();
      });
    });
  });
});
