const fs = require('fs');
const path = require('path');

describe.skip('logger_nr', () => {
  function loadLogger(config) {
    if ((config !== undefined && typeof config !== 'object') || config === null) {
      throw new Error('Config must be an object');
    }
    const loggerPath = path.resolve(__dirname, '../logger_nr.js');
    let loggerSource = fs.readFileSync(loggerPath, 'utf8');

    const mocks = {
      newRelicApiKey: 'mock-api-key',
      version: 'mock-version',
      proposition: 'mock-proposition',
      provider: 'mock-provider',
      territory: 'mock-territory',
    };

    Object.assign(mocks, config);

    for (const key in mocks) {
      const placeholder = new RegExp(`'\\$\\{${key}\\}'`, 'g');
      loggerSource = loggerSource.replace(placeholder, `'${mocks[key]}'`);
    }

    new Function(loggerSource)();
  }

  const NR_URL = 'https://log-api.newrelic.com/log/v1';

  beforeEach(() => {
    global.window = {
      utils: {
        request: jest.fn(),
      },
    };
  });

  afterEach(() => {
    delete global.window;
    delete global.tizen;
    jest.restoreAllMocks();
  });

  it.skip('should attach logger object to window.utils', () => {
    loadLogger();
    expect(global.window.utils.logger).toBeInstanceOf(Object);
    expect(global.window.utils.logger.info).toBeInstanceOf(Function);
    expect(global.window.utils.logger.error).toBeInstanceOf(Function);
    expect(global.window.utils.logger.initialise).toBeInstanceOf(Function);
    expect(global.window.utils.logger.LOG_LEVEL).toEqual({
      INFO: 'info',
      ERROR: 'error',
    });
  });

  describe.skip('createId', () => {
    it.skip('should return incrementing IDs', () => {
      loadLogger();
      expect(global.window.utils.logger.createId()).toBe(1);
      expect(global.window.utils.logger.createId()).toBe(2);
      expect(global.window.utils.logger.createId()).toBe(3);
    });
  });

  describe.skip('append', () => {
    it.skip.each([
      ['undefined', undefined],
      ['null', null],
      ['a string', 'abc'],
      ['an object', {}],
      ['zero', 0],
      ['a negative number', -1],
    ])('should throw an error if logId is %s', (_, invalidId) => {
      loadLogger();
      const expectedError = 'Log ID must be a valid number greater than 0';

      expect(() => global.window.utils.logger.info(invalidId, { data: 'test' })).toThrow(
        expectedError
      );
    });

    it.skip('should store data against a logId', () => {
      loadLogger();
      const logId = global.window.utils.logger.createId();
      const dataToAppend = { custom: 'data' };
      global.window.utils.logger.append(logId, dataToAppend);
      global.window.utils.logger.send(logId);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('custom', 'data');
      expect(sentBody).toEqual(
        expect.objectContaining({
          custom: 'data',
        })
      );
    });
  });

  describe.skip('addBaseData', () => {
    beforeEach(() => {
      loadLogger();
    });

    it.skip('should add new properties to the base data for all subsequent logs', () => {
      global.window.utils.logger.addBaseData({ appName: 'TestApp', sessionId: '123' });
      global.window.utils.logger.send({ message: 'test log' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          appName: 'TestApp',
          sessionId: '123',
        })
      );
    });

    it.skip('should override existing properties in the base data', () => {
      global.window.utils.logger.addBaseData({ proposition: 'new-proposition' });
      global.window.utils.logger.send({ message: 'test log' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('proposition', 'new-proposition');
      expect(sentBody).toEqual(
        expect.objectContaining({
          proposition: 'new-proposition',
        })
      );
    });
  });

  describe.skip('send', () => {
    it.skip('should not call request if the New Relic API key is an empty string', function () {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      loadLogger({
        newRelicApiKey: '', // Simulate an empty API key from the build process
      });

      global.window.utils.logger.info({ message: 'this should not be sent' });

      expect(global.window.utils.request).not.toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledTimes(1);
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'New Relic API key is missing. Logs will not be sent.'
      );
    });

    it.skip('should call request with the correct payload when the API key is present', function () {
      loadLogger();

      global.window.utils.logger.info({ message: 'this should be sent' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      expect(global.window.utils.request).toHaveBeenCalledWith(
        expect.objectContaining({
          url: NR_URL,
          method: 'POST',
          headers: expect.objectContaining({
            'Api-Key': 'mock-api-key',
          }),
        })
      );
    });

    it.skip('should call request with the correct URL, method, and headers', () => {
      loadLogger();
      global.window.utils.logger.info({ message: 'test' });
      expect(global.window.utils.request).toHaveBeenCalledWith({
        url: NR_URL,
        method: 'POST',
        headers: {
          'Api-Key': 'mock-api-key',
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: expect.any(String),
      });
    });

    it.skip('should send a log with default base data', () => {
      loadLogger();
      const messageData = { message: 'Initial log' };
      global.window.utils.logger.info(messageData);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          containerVersion: 'mock-version',
          level: 'info',
          logtype: 'container',
          osName: 'Tizen',
          platform: 'SAMSUNG',
          proposition: 'mock-proposition',
          provider: 'mock-provider',
          territory: 'mock-territory',
          message: 'Initial log',
        })
      );
    });

    it.skip('should include data from addBaseData in the payload', function () {
      loadLogger();

      global.window.utils.logger.initialise({
        containerSession: 'session-123',
          shouldLogValue: true,
      });

      global.window.utils.logger.info({ message: 'log with custom base data' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          userId: 'user-123',
          customDimension: 'valueA',
          message: 'log with custom base data',
        })
      );
    });

    it.skip('should combine base data and appended data when sending by ID', () => {
      loadLogger();
      global.window.utils.logger.info({
        containerSession: 'session-123',
        shouldLogValue: true,
      });
      global.window.utils.logger.info({ component: 'menu', message: 'Menu opened' });

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          sessionId: 'session-xyz',
          component: 'menu',
          message: 'Menu opened',
          level: 'info',
        })
      );
    });

    it.skip('should delete appended data after sending', () => {
      loadLogger();
      const logId = global.window.utils.logger.createId();
      global.window.utils.logger.append(logId, { data: 'once' });
      global.window.utils.logger.send(logId);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      global.window.utils.request.mockClear();

      global.window.utils.logger.send(logId);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).not.toHaveProperty('data');
    });
  });

  describe.skip('logError', () => {
    beforeEach(() => {
      loadLogger();
    });

    it.skip('should call send with a formatted error object', () => {
      const error = new Error('Something went wrong');
      error.name = 'TestError';
      error.stack = 'stack trace here';
      const message = 'Caught a test error';

      global.window.utils.logger.error(message, error, '');

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          level: 'error',
          message: message,
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack,
        })
      );
    });
  });
});
