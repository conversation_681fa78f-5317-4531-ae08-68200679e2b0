const fs = require('fs');
const path = require('path');

describe('logger_nr', () => {
  function loadLogger(config) {
    if ((config !== undefined && typeof config !== 'object') || config === null) {
      throw new Error('Config must be an object');
    }
    const loggerPath = path.resolve(__dirname, '../logger_nr.js');
    let loggerSource = fs.readFileSync(loggerPath, 'utf8');

    const mocks = {
      newRelicApiKey: 'mock-api-key',
      version: 'mock-version',
      proposition: 'mock-proposition',
      provider: 'mock-provider',
      territory: 'mock-territory',
    };

    Object.assign(mocks, config);

    for (const key in mocks) {
      const placeholder = new RegExp(`'\\$\\{${key}\\}'`, 'g');
      loggerSource = loggerSource.replace(placeholder, `'${mocks[key]}'`);
    }

    new Function(loggerSource)();
  }

  const NR_URL = 'https://log-api.newrelic.com/log/v1';

  beforeEach(() => {
    global.window = {
      utils: {
        request: jest.fn(),
      },
    };
  });

  afterEach(() => {
    delete global.window;
    delete global.tizen;
    jest.restoreAllMocks();
  });

  it('should attach logger object to window.utils', () => {
    loadLogger();
    expect(global.window.utils.logger).toBeInstanceOf(Object);
    expect(global.window.utils.logger.info).toBeInstanceOf(Function);
    expect(global.window.utils.logger.error).toBeInstanceOf(Function);
    expect(global.window.utils.logger.initialise).toBeInstanceOf(Function);
  });

  describe('initialise', () => {
    it('should set up base data for logging', () => {
      loadLogger();
      const params = {
        containerSession: 'test-session-123',
        osVersion: 'Tizen 5.0',
        shouldLog: true,
      };

      // This should not throw
      expect(() => global.window.utils.logger.initialise(params)).not.toThrow();
    });
  });

  describe('info', () => {
    it('should send info logs when shouldLog is true', () => {
      loadLogger();
      global.window.utils.logger.initialise({
        containerSession: 'test-session',
        osVersion: 'Tizen 5.0',
        shouldLog: true,
      });

      global.window.utils.logger.info({ message: 'test info log' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      expect(global.window.utils.request).toHaveBeenCalledWith({
        url: NR_URL,
        method: 'POST',
        headers: {
          'Api-Key': 'mock-api-key',
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: expect.stringContaining('"message":"test info log"'),
      });
    });

    it('should not send logs when shouldLog is false', () => {
      loadLogger();
      global.window.utils.logger.initialise({
        containerSession: 'test-session',
        osVersion: 'Tizen 5.0',
        shouldLog: false,
      });

      global.window.utils.logger.info({ message: 'test info log' });

      expect(global.window.utils.request).not.toHaveBeenCalled();
    });

    it('should not send logs when API key is missing', () => {
      const consoleWarnSpy = jest.spyOn(console, 'warn').mockImplementation();
      loadLogger({ newRelicApiKey: '' });

      global.window.utils.logger.initialise({
        containerSession: 'test-session',
        osVersion: 'Tizen 5.0',
        shouldLog: true,
      });

      global.window.utils.logger.info({ message: 'test info log' });

      expect(global.window.utils.request).not.toHaveBeenCalled();
      expect(consoleWarnSpy).toHaveBeenCalledWith(
        'New Relic API key is missing. Logs will not be sent.'
      );

      consoleWarnSpy.mockRestore();
    });
  });

  describe('error', () => {
    it('should send error logs with error details', () => {
      loadLogger();
      global.window.utils.logger.initialise({
        containerSession: 'test-session',
        osVersion: 'Tizen 5.0',
        shouldLog: true,
      });

      const error = new Error('Test error');
      error.name = 'TestError';
      error.stack = 'Error stack trace';

      global.window.utils.logger.error('Something went wrong', error, { extra: 'data' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const requestCall = global.window.utils.request.mock.calls[0][0];
      const body = JSON.parse(requestCall.body);

      expect(body).toEqual(
        expect.objectContaining({
          level: 'error',
          message: 'Something went wrong',
          errorMessage: 'Test error',
          errorName: 'TestError',
          errorStack: 'Error stack trace',
          extra: 'data',
          containerSession: 'test-session',
          osVersion: 'Tizen 5.0',
        })
      );
    });
  });
});
