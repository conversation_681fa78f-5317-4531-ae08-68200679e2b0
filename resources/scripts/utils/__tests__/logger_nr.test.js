const fs = require('fs');
const path = require('path');

describe('logger_nr', () => {
  /**
   * Loads the logger script with a specific New Relic API key.
   * This helper function simulates the build process by replacing the API key placeholder
   * in the source file before executing it. This allows testing different key scenarios.
   *
   * @param {string} api<PERSON>ey The API key to inject into the script.
   */
  function loadLoggerWithKey(apiKey) {
    const loggerPath = path.resolve(__dirname, '../logger_nr.js');
    const loggerSource = fs.readFileSync(loggerPath, 'utf8');
    const sourceWithKey = loggerSource.replace("'${newRelicApiKey}'", "'" + api<PERSON><PERSON> + "'");

    // Execute the modified script: new Function() runs it in the global scope.
    // eslint-disable-next-line no-new-func
    new Function(sourceWithKey)();
  }

  const NR_URL = 'https://log-api.newrelic.com/log/v1';
  const mockPlatformVersion = '6.5';

  beforeEach(() => {
    global.tizen = {
      systeminfo: {
        getCapability: jest.fn().mockReturnValue(mockPlatformVersion),
      },
    };

    global.window = {
      utils: {
        request: jest.fn(),
      },
    };

    // The script is an IIFE that modifies window.utils, so we need to load it for each test
    // after the window object is set up.
    jest.isolateModules(function () {
      require('../logger_nr.js');
    });
  });

  afterEach(() => {
    delete global.window;
    delete global.tizen;
    jest.restoreAllMocks();
  });

  it('should attach logger object to window.utils', () => {
    expect(global.window.utils.logger).toBeInstanceOf(Object);
    expect(global.window.utils.logger.send).toBeInstanceOf(Function);
    expect(global.window.utils.logger.logError).toBeInstanceOf(Function);
    expect(global.window.utils.logger.addBaseData).toBeInstanceOf(Function);
    expect(global.window.utils.logger.append).toBeInstanceOf(Function);
    expect(global.window.utils.logger.createId).toBeInstanceOf(Function);
    expect(global.window.utils.logger.LOG_LEVEL).toEqual({
      INFO: 'info',
      ERROR: 'error',
    });
  });

  describe('createId', () => {
    it('should return incrementing IDs', () => {
      expect(global.window.utils.logger.createId()).toBe(1);
      expect(global.window.utils.logger.createId()).toBe(2);
      expect(global.window.utils.logger.createId()).toBe(3);
    });
  });

  describe('append', () => {
    it('should throw an error if logId is not provided', () => {
      expect(() => global.window.utils.logger.append(undefined, { data: 'test' })).toThrow(
        'Log ID is required'
      );
    });

    it('should store data against a logId', () => {
      loadLoggerWithKey('test-api-key');
      const logId = global.window.utils.logger.createId();
      const dataToAppend = { custom: 'data' };
      global.window.utils.logger.append(logId, dataToAppend);
      global.window.utils.logger.send(logId);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('custom', 'data');
      expect(sentBody).toEqual(
        expect.objectContaining({
          custom: 'data',
        })
      );
    });
  });

  describe('addBaseData', () => {
    beforeEach(() => {
      loadLoggerWithKey('test-api-key');
    });

    it('should add new properties to the base data for all subsequent logs', () => {
      global.window.utils.logger.addBaseData({ appName: 'TestApp', sessionId: '123' });
      global.window.utils.logger.send({ message: 'test log' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          appName: 'TestApp',
          sessionId: '123',
        })
      );
    });

    it('should override existing properties in the base data', () => {
      global.window.utils.logger.addBaseData({ proposition: 'new-proposition' });
      global.window.utils.logger.send({ message: 'test log' });

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toHaveProperty('proposition', 'new-proposition');
      expect(sentBody).toEqual(
        expect.objectContaining({
          proposition: 'new-proposition',
        })
      );
    });
  });

  it('should not call request if the New Relic API key is an empty string', function () {
    loadLoggerWithKey(''); // Simulate an empty API key from the build process

    global.window.utils.logger.send({ message: 'this should not be sent' });

    expect(global.window.utils.request).not.toHaveBeenCalled();
  });

  it('should call request with the correct payload when the API key is present', function () {
    loadLoggerWithKey('test-api-key');

    global.window.utils.logger.send({ message: 'this should be sent' });

    expect(global.window.utils.request).toHaveBeenCalledTimes(1);
    expect(global.window.utils.request).toHaveBeenCalledWith(
      expect.objectContaining({
        url: NR_URL,
        method: 'POST',
        headers: expect.objectContaining({
          'Api-Key': 'test-api-key',
        }),
      })
    );
  });

  it('should include data from addBaseData in the payload', function () {
    loadLoggerWithKey('test-api-key');

    // Add custom base data
    global.window.utils.logger.addBaseData({
      userId: 'user-123',
      customDimension: 'valueA',
    });

    // Send a log
    global.window.utils.logger.send({ message: 'log with custom base data' });

    expect(global.window.utils.request).toHaveBeenCalledTimes(1);
    const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
    expect(sentBody).toEqual(
      expect.objectContaining({
        userId: 'user-123',
        customDimension: 'valueA',
        message: 'log with custom base data',
      })
    );
  });

  it('should not call request if the New Relic API key is the placeholder string', function () {
    // Note: The default beforeEach loads the logger without replacing the key,
    // so NR_KEY is the placeholder string.
    global.window.utils.logger.send({ message: 'this should not be sent' });
    expect(global.window.utils.request).not.toHaveBeenCalled();
  });

  describe('send', () => {
    const newRelicApiKey = 'a-valid-test-key';
    beforeEach(() => {
      // Reload the logger with a valid key for this suite of tests
      loadLoggerWithKey(newRelicApiKey);
    });

    it('should call request with the correct URL, method, and headers', () => {
      global.window.utils.logger.send({ message: 'test' });
      expect(global.window.utils.request).toHaveBeenCalledWith({
        url: NR_URL,
        method: 'POST',
        headers: {
          'Api-Key': newRelicApiKey,
          'Content-Type': 'application/json',
          Accept: 'application/json',
        },
        body: expect.any(String),
      });
    });

    it('should send a log with default base data', () => {
      const messageData = { message: 'Initial log' };
      global.window.utils.logger.send(messageData);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          containerVersion: '${version}',
          level: 'info',
          logtype: 'container',
          osName: 'Tizen',
          osVersion: mockPlatformVersion,
          platform: 'SAMSUNG',
          proposition: '${proposition}',
          provider: '${provider}',
          territory: '${territory}',
          message: 'Initial log',
        })
      );
    });

    it('should combine base data and appended data when sending by ID', () => {
      const logId = global.window.utils.logger.createId();
      global.window.utils.logger.addBaseData({ sessionId: 'session-xyz' });
      global.window.utils.logger.append(logId, { component: 'menu', message: 'Menu opened' });
      global.window.utils.logger.send(logId);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          sessionId: 'session-xyz',
          component: 'menu',
          message: 'Menu opened',
          level: 'info',
        })
      );
    });

    it('should delete appended data after sending', () => {
      const logId = global.window.utils.logger.createId();
      global.window.utils.logger.append(logId, { data: 'once' });
      global.window.utils.logger.send(logId);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);
      global.window.utils.request.mockClear();

      // Send again with the same ID, the appended data should be gone
      global.window.utils.logger.send(logId);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).not.toHaveProperty('data');
    });
  });

  describe('logError', () => {
    beforeEach(() => {
      loadLoggerWithKey('test-api-key');
    });

    it('should call send with a formatted error object', () => {
      const error = new Error('Something went wrong');
      error.name = 'TestError';
      error.stack = 'stack trace here';
      const message = 'Caught a test error';

      global.window.utils.logger.logError(message, error);

      expect(global.window.utils.request).toHaveBeenCalledTimes(1);

      const sentBody = JSON.parse(global.window.utils.request.mock.calls[0][0].body);
      expect(sentBody).toEqual(
        expect.objectContaining({
          level: 'error',
          message: message,
          errorMessage: error.message,
          errorName: error.name,
          errorStack: error.stack,
        })
      );
    });
  });
});
