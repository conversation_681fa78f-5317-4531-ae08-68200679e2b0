/**
 * @fileoverview
 * Utility functions for working with URLs
 */

(function (utils) {
  /**
   * Converts an object of key-value pairs into a URL query string.
   * Each key-value pair is URL-encoded and joined by '&'.
   * @param {Object<string, string|number|boolean>} params An object containing key-value pairs to be converted into a query string.
   * @returns {string} The URL query string (e.g., "key1=value1&key2=value2").
   */
  function toQueryString(params) {
    var query = [];
    for (var key in params) {
      if (Object.prototype.hasOwnProperty.call(params, key)) {
        query.push(encodeURIComponent(key) + '=' + encodeURIComponent(params[key]));
      }
    }
    return query.join('&');
  }

  /**
   * addQueryParamss a query string (generated from the provided params object) to a given URL.
   * It correctly handles whether the URL already has query parameters.
   * @param {string} baseUrl The base URL to addQueryParams the query string to.
   * @param {Object<string, string|number|boolean>} params An object containing key-value pairs
   * to be converted into a query string.
   * @returns {string} The URL with the addQueryParamsed query string, or the original URL if params is empty.
   */
  function addQueryParams(baseUrl, params) {
    var queryString = toQueryString(params);
    if (!queryString) {
      return baseUrl;
    }
    var url = baseUrl.charAt(baseUrl.length - 1) === '?' ? baseUrl.slice(0, -1) : baseUrl;
    var char = url.indexOf('?') === -1 ? '?' : '&';
    return url + char + queryString;
  }

  /**
   * Parses the query string from the current `window.location.search`
   * and returns an object containing the key-value pairs of the parameters.
   * @returns {Object<string, string>} An object where keys are query parameter names
   * and values are their corresponding decoded values.
   */
  function getQueryParams() {
    var queryParams = {};
    var search = window.location.search.substring(1);

    search.replace(/([^=&]+)=([^&]*)/g, function (_, key, value) {
      queryParams[decodeURIComponent(key)] = decodeURIComponent(value);
    });

    return queryParams;
  }

  /**
   * Returns the origin of a given URL
   * @param {string} [url] - Required. The URL string from which to extract the origin.
   * @returns {string} The origin of the URL (e.g., "https://example.com:8080").
   *
   * @example
   * getOrigin('https://tv.clients.peacocktv.com/samsung-2016.html?container=tizen&container_version=6.2.200');
   * // Returns: "https://tv.clients.peacocktv.com"
   */
  function getOrigin(url) {
    // Regex to extract protocol, hostname, and port from the URL
    // Matches: protocol://hostname:port
    var match = url.match(/^([a-z0-9.+-]+:)?\/\/([^/:?#]+)(:\d+)?/i);
    if (match) {
      return (match[1] || window.location.protocol) + '//' + match[2] + (match[3] || '');
    }
    return '';
  }

  utils.url = {
    addQueryParams: addQueryParams,
    getQueryParams: getQueryParams,
    getOrigin: getOrigin,
  };
})(window.utils || (window.utils = {}));
