{"name": "SkyShowtime", "author": "SkyShowtime", "provider": "SKYSHOWTIME", "proposition": "SKYSHOWTIME", "defaultLanguage": "en", "samFileName": "sam_preview_hash_sst.json", "errorMessage": "// message in template", "adInfoPrivilege": "<tizen:privilege name=\"http://developer.samsung.com/privilege/adinfo\"/>", "edenPreviewBackgroundService": "<tizen:service id=\"${edenPreviewServiceId}\">\n\t\t<tizen:content src=\"js/eden_service.js\" />\n\t\t<tizen:name>service</tizen:name>\n\t\t<tizen:description>Service Application</tizen:description>\n\t\t<tizen:metadata key=\"http://samsung.com/tv/metadata/use.preview\" value=\"bg_service\" />\n\t\t<tizen:category name=\"http://tizen.org/category/service\" />\n\t</tizen:service>", "cwBackgroundService": " ", "query": "", "applicationId": "skysh0WTIM.SkyShowtime", "authorUrl": "https://skyshowtime.com/", "accelerator": "", "hardwareKeyEvent": "disable", "css": {"fontRegular": "skyshowtime_sans-book.woff", "fontBold": "skyshowtime_sans-bold.woff", "retryButtonColor": "#6d5ff9", "retryButtonTextColor": "white"}, "html": {"errorTitle": "Everybody stay calm!", "errorMessage": "SkyShowtime isn't loading. Please try again in a bit.", "localisationURL": "https://tv.clients.skyshowtime.com/ls/localisation", "labelsURL": "https://atom.skyshowtime.com/adapter-calypso/v3/labels", "splashZoom": 37.5}, "environments": {"production": {"url": "https://tv.clients.skyshowtime.com/samsung.html", "sam": "sam.disco.skyshowtime.com/samsung/eden", "newRelicApiKey": "63b60c231afbaf6904715128341df940FFFFNRAL"}, "preview": {"url": "https://tv.clients.skyshowtime.com/samsung-preview.html", "sam": "sam.disco.skyshowtime.com/samsung/eden"}, "rc": {"applicationId": "skysh0WTRC.SkyShowtimeRC", "url": "https://tv.clients.skyshowtime.com/samsung-rc.html", "sam": "sam.disco.skyshowtime.com/samsung/eden"}, "stable": {"applicationId": "skysh0STAB.SkyShowtimeStable", "url": "https://tv.clients.stable-int.skyshowtime.com/samsung.html", "sam": "sam.disco.stable-int.skyshowtime.com/samsung/eden"}, "stable-preview": {"applicationId": "skyshSPREV.SkyShowtimeStablePreview", "url": "https://tv.clients.stable-int.skyshowtime.com/samsung-preview.html", "sam": "sam.disco.skyshowtime.com/samsung/eden"}, "proton": {"applicationId": "skysPROTON.SkyShowtimeProton", "url": "https://tv-proton.clients.skyshowtime.com/samsung-proton.html", "sam": "sam.disco.skyshowtime.com/samsung/eden"}}}