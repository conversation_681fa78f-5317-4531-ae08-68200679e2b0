/**
 * @fileoverview
 * Shared test utilities for mocking file system and other common test setup
 */

/**
 * Sets up a comprehensive mock for the 'fs' module.
 * It clears any previous mock state and then configures mock implementations
 * for common synchronous and asynchronous fs functions based on the provided
 * file structure.
 *
 * @param {Object} fs - The mocked 'fs' module. Must have `__setMockFiles` method.
 * @param {Object<string, string|string[]>} [mockFiles={}] - An object where keys are file paths
 *   and values are either the file's string content or an array of filenames for a directory.
 * @example
 * const fs = require('fs');
 * const { setupMockFs } = require('./mock-helpers');
 *
 * jest.mock('fs');
 *
 * beforeEach(() => {
 *   setupMockFs(fs, {
 *     '/path/to/file.txt': 'file content',
 *     '/path/to/directory': ['file1.txt', 'file2.txt'],
 *     '/path/to/directory/file1.txt': 'content 1',
 *     '/path/to/directory/file2.txt': 'content 2',
 *   });
 * });
 */
function setupMockFs(fs, mockFiles = {}) {
  if (!fs || !fs.__setMockFiles) {
    throw new Error('setupMockFs requires a properly mocked fs module with __setMockFiles method');
  }

  // This is added to ensure tests isolation.
  // Remove it if you ever want to allow calling setupMockFs multiple times in a single test
  clearMockFs(fs);

  fs.__setMockFiles(mockFiles);

  // Get the mock files for use in implementations
  const files = fs.__getMockFiles();

  // Set up mock implementations for all fs functions
  fs.existsSync.mockImplementation((filePath) => files[filePath] !== undefined);

  fs.readdirSync.mockImplementation((directoryPath) => {
    const dirFiles = files[directoryPath] || [];
    // Filter out proposition/territory/environment directories from root resource scans
    if (directoryPath.match(/resources\/[^/]+$/)) {
      const RESOURCE_HIERARCHY_FOLDERS = ['proposition', 'territory', 'environment'];
      return dirFiles.filter((file) => !RESOURCE_HIERARCHY_FOLDERS.includes(file));
    }
    return dirFiles;
  });

  fs.statSync.mockImplementation((filePath) => ({
    isDirectory: () => Array.isArray(files[filePath]),
  }));

  fs.readFileSync.mockImplementation((filePath) => files[filePath]);

  // Set up async fs functions for output-data style tests
  if (fs.writeFile && fs.writeFile.mockImplementation) {
    fs.writeFile.mockImplementation((_, __, cb) => {
      process.nextTick(() => cb(null));
    });
  }

  if (fs.mkdir && fs.mkdir.mockImplementation) {
    fs.mkdir.mockImplementation((_, cb) => {
      process.nextTick(() => cb(null));
    });
  }
}

/**
 * Resets the state of the mocked 'fs' module to ensure test isolation.
 * This function clears all previously defined mock files and resets the
 * call history for all mocked fs functions (e.g., `readFileSync`, `writeFile`).
 * It is designed to be used in a `beforeEach` hook to guarantee a clean
 * slate for every test.
 *
 * @param {Object} fs - The mocked 'fs' module from `jest.mock('fs')`.
 * @example
 * const fs = require('fs');
 * const { clearMockFs } = require('./mock-helpers');
 *
 * jest.mock('fs');
 *
 * beforeEach(() => {
 *   clearMockFs(fs);
 * });
 */
function clearMockFs(fs) {
  if (!fs) {
    return;
  }

  // Clear mock files state
  if (fs.__setMockFiles) {
    fs.__setMockFiles({});
  }

  // Clear mock call history for all fs functions
  const fsFunctions = [
    'existsSync',
    'readdirSync',
    'statSync',
    'readFileSync',
    'writeFile',
    'mkdir',
  ];
  fsFunctions.forEach((fnName) => {
    if (fs[fnName] && fs[fnName].mockClear) {
      fs[fnName].mockClear();
    }
  });
}

module.exports = {
  setupMockFs,
  clearMockFs,
};
