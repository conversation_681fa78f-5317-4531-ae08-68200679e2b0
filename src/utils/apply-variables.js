const variableResolvers = require('./variable-resolvers');

const defaultOptions = { isFolder: false, recursive: false };

function defaultResolver(configVariables) {
  return Object.keys(configVariables).reduce((obj, variable) => {
    obj[variable] = (config) => config[variable];
    return obj;
  }, {});
}

function generateVariableResolvers(configVariables) {
  return {
    ...defaultResolver(configVariables),
    ...variableResolvers,
  };
}
function validateFile(fileData) {
  const unresolvedVariables = fileData.match(/\${.+}/g);

  if (unresolvedVariables) {
    const unresolvedList = unresolvedVariables.map((v) => v.substring(2, v.length - 1));
    const message = `Unresolved variables found: ${unresolvedList.join(', ')}`;
    throw new Error(message);
  }

  return fileData;
}

function replaceVariableInFile({ variable, value, file }) {
  return file.toString().replace(new RegExp(`\\$\{${variable}}`, 'g'), value);
}

function applyVariablesToFile(configVariables, initialFile) {
  const generatedVariableResolvers = generateVariableResolvers(configVariables);
  const updatedFile = Object.entries(generatedVariableResolvers).reduce(
    (file, [variable, resolver]) => {
      const value = resolver(configVariables);

      // If value is null or undefined, the variable should be erased from the file
      if (!value) {
        return replaceVariableInFile({ variable, value: '', file });
      }

      return replaceVariableInFile({ variable, value, file });
    },
    initialFile
  );

  return validateFile(updatedFile);
}

function isDirectory(target) {
  return target && typeof target === 'object' && !Buffer.isBuffer(target) && !Array.isArray(target);
}

function applyVariables(configVariables, target, options = defaultOptions) {
  if (!options.isFolder) {
    return applyVariablesToFile(configVariables, target);
  }

  const updatedFiles = {};
  for (const fileName in target) {
    if (!Object.hasOwn(target, fileName)) {
      continue;
    }
    const fileData = target[fileName];
    if (isDirectory(fileData)) {
      if (options.recursive) {
        // Recursively apply to subdirectory
        const newOptions = { ...options, isFolder: true };
        updatedFiles[fileName] = applyVariables(configVariables, fileData, newOptions);
      }
      continue;
    }
    // Apply to file
    updatedFiles[fileName] = applyVariablesToFile(configVariables, fileData);
  }
  return updatedFiles;
}

module.exports = { applyVariables };
