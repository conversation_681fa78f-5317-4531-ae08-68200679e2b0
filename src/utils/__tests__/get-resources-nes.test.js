const path = require('path');
const fs = require('fs');
const { setupMockFs, clearMockFs } = require('../../__test-utils__/mock-helpers');
const { getMergedResourceFiles } = require('../get-resources');

jest.mock('fs');

const resource = 'resource';
const proposition = 'proposition';
const territory = 'territory';
const environment = 'environment';

beforeEach(() => {
  clearMockFs(fs);
});

describe('getMergedResourceFiles', () => {
  test('should return root resource if no proposition, territory or environment resource', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: [],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: [],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'root',
    });
  });

  test('should return proposition resource if no territory or environment resource', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: [],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'proposition',
    });
  });

  test('should return territory resource if no environment resource', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/logo.png`)]:
        'territory',
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'territory',
    });
  });

  test('should return environment resource if exists', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/logo.png`)]: 'proposition',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: ['logo.png'],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/logo.png`)]:
        'territory',
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: ['logo.png'],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}/logo.png`
      )]: 'environment',
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'environment',
    });
  });
});

describe('getMergedResourceFiles recursively', () => {
  test('should return nested object for subdirectories when recursive is true', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['file1.txt', 'subdir'],
      [path.join(process.cwd(), `resources/${resource}/file1.txt`)]: 'root-file',
      [path.join(process.cwd(), `resources/${resource}/subdir`)]: ['nested.txt'],
      [path.join(process.cwd(), `resources/${resource}/subdir/nested.txt`)]: 'nested-file',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: ['subdir'],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/subdir`)]: ['nested.txt'],
      // This will overwrite the root subdir file
      [path.join(process.cwd(), `resources/${resource}/${proposition}/subdir/nested.txt`)]:
        'prop-nested-file',
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: [],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });

    const result = getMergedResourceFiles(resource, proposition, territory, environment, true);

    expect(result).toStrictEqual({
      'file1.txt': 'root-file',
      subdir: {
        'nested.txt': 'prop-nested-file',
      },
      territory: {
        environment: {},
      },
      environment: {},
    });
  });

  test('should ignore subdirectories when recursive is false', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['file1.txt', 'subdir'],
      [path.join(process.cwd(), `resources/${resource}/file1.txt`)]: 'root-file',
      [path.join(process.cwd(), `resources/${resource}/subdir`)]: ['nested.txt'],
      [path.join(process.cwd(), `resources/${resource}/subdir/nested.txt`)]: 'nested-file',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: [],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: [],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });

    const result = getMergedResourceFiles(resource, proposition, territory, environment, false);

    expect(result).toStrictEqual({ 'file1.txt': 'root-file' });
  });
});

describe('getBaseConfigXML', () => {
  test('should read the config.xml', () => {
    const { getBaseConfigXML } = require('../get-resources');
    const mockConfigContent = '<?xml version="1.0" encoding="UTF-8"?><widget></widget>';
    const configPath = path.join(process.cwd(), 'resources/config.xml');
    setupMockFs(fs, {
      [configPath]: mockConfigContent,
    });
    const result = getBaseConfigXML();
    expect(fs.readFileSync).toHaveBeenCalledTimes(1);
    expect(fs.readFileSync).toHaveBeenCalledWith(configPath, 'utf-8');
    expect(result).toBe(mockConfigContent);
  });
});

describe('getMergedResourceFiles with ignored files', () => {
  test('should ignore test files and directories', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: [
        'logo.png',
        'some.test.js',
        'some.spec.js',
        '__tests__',
        '__mocks__',
        '__test-utils__',
      ],
      [path.join(process.cwd(), `resources/${resource}/logo.png`)]: 'root',
      [path.join(process.cwd(), `resources/${resource}/some.test.js`)]: 'test file content',
      [path.join(process.cwd(), `resources/${resource}/some.spec.js`)]: 'spec file content',
      [path.join(process.cwd(), `resources/${resource}/__tests__`)]: ['another.test.js'],
      [path.join(process.cwd(), `resources/${resource}/__tests__/another.test.js`)]:
        'another test file content',
      [path.join(process.cwd(), `resources/${resource}/__mocks__`)]: ['a.js'],
      [path.join(process.cwd(), `resources/${resource}/__mocks__/a.js`)]: 'a mock',
      [path.join(process.cwd(), `resources/${resource}/__test-utils__`)]: ['util.js'],
      [path.join(process.cwd(), `resources/${resource}/__test-utils__/util.js`)]: 'a test util',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: [],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: [],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });
    expect(getMergedResourceFiles(resource, proposition, territory, environment)).toStrictEqual({
      'logo.png': 'root',
    });
  });

  test('should ignore test files and directories recursively', () => {
    setupMockFs(fs, {
      [path.join(process.cwd(), `resources/${resource}`)]: ['file1.txt', 'subdir'],
      [path.join(process.cwd(), `resources/${resource}/file1.txt`)]: 'root-file',
      [path.join(process.cwd(), `resources/${resource}/subdir`)]: [
        'nested.txt',
        '__tests__',
        'b.test.js',
      ],
      [path.join(process.cwd(), `resources/${resource}/subdir/nested.txt`)]: 'nested-file',
      [path.join(process.cwd(), `resources/${resource}/subdir/__tests__`)]: ['a.test.js'],
      [path.join(process.cwd(), `resources/${resource}/subdir/__tests__/a.test.js`)]:
        'should be ignored',
      [path.join(process.cwd(), `resources/${resource}/subdir/b.test.js`)]: 'should be ignored',
      [path.join(process.cwd(), `resources/${resource}/${proposition}`)]: [],
      [path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`)]: [],
      [path.join(
        process.cwd(),
        `resources/${resource}/${proposition}/${territory}/${environment}`
      )]: [],
    });

    const result = getMergedResourceFiles(resource, proposition, territory, environment, true);

    expect(result).toStrictEqual({
      'file1.txt': 'root-file',
      subdir: {
        'nested.txt': 'nested-file',
      },
      territory: {
        environment: {},
      },
      environment: {},
    });
  });
});
