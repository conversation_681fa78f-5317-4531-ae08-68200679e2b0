const mockVariableResolvers = {
  VARIABLE_1: jest.fn(() => 'RESOLVED_VALUE'),
  VARIABLE_2: jest.fn(() => 'RESOLVED_VALUE_2'),
  VARIABLE_3: jest.fn(() => 'RESOLVED_VALUE_3'),
};
jest.mock('../variable-resolvers', () => mockVariableResolvers);

const { applyVariables } = require('../apply-variables');

const mockConfig = { config: 'data' };

const file1 = '${config}XMLwith${VARIABLE_1}and${VARIABLE_2}';
const file2 = 'JSwith${VARIABLE_3}';
const fileWithUnresolvableVariable = '${config}XMLwith${UNRESOLVABLE_VARIABLE}';

const files = {
  file1,
  file2,
};

describe('applyVariables', () => {
  test('should call variable resolvers', async () => {
    await applyVariables(mockConfig, file1);
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(1);
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledWith(mockConfig);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(1);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledWith(mockConfig);
  });

  test('should replace all variables with result of variable resolver', async () => {
    const result = await applyVariables(mockConfig, file1);
    expect(result).toBe('dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2');
  });

  test('should throw an error for unresolvable variables', () => {
    const action = () => applyVariables(mockConfig, fileWithUnresolvableVariable);
    expect(action).toThrow('Unresolved variables found: UNRESOLVABLE_VARIABLE');
  });
});

describe('applyVariables (folder)', () => {
  test('should replace all variables with result of variable resolver', async () => {
    await applyVariables(mockConfig, files, { isFolder: true });
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledWith(mockConfig);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledWith(mockConfig);
    expect(mockVariableResolvers.VARIABLE_3).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_3).toHaveBeenCalledWith(mockConfig);
  });

  test('should applyResolvedVariablesToFile for each file', async () => {
    const result = await applyVariables(mockConfig, files, { isFolder: true });
    expect(result).toStrictEqual({
      file1: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
      file2: 'JSwithRESOLVED_VALUE_3',
    });
  });
});

describe('applyVariables (folder) (recursive)', () => {
  test('should throw an error for nested unresolvable variables', () => {
    const action = () =>
      applyVariables(
        mockConfig,
        {
          file: file1,
          subdir: {
            nestedFile: file2,
            nestedUnresolvable: fileWithUnresolvableVariable,
          },
        },
        { isFolder: true, recursive: true }
      );
    expect(action).toThrow('Unresolved variables found: UNRESOLVABLE_VARIABLE');
  });

  const nestedFiles = {
    file: file1,
    subdir: {
      nestedFile: file2,
    },
  };

  test('should recursively replace all variables with result of variable resolver', async () => {
    await applyVariables(mockConfig, nestedFiles, { isFolder: true, recursive: true });
    // VARIABLE_1 and VARIABLE_2 should be called once for each file containing them (file + subdir.nestedFile)
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(2);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(2);
  });

  test('should recursively applyResolvedVariablesToFile for each file', async () => {
    const result = await applyVariables(mockConfig, nestedFiles, {
      isFolder: true,
      recursive: true,
    });
    expect(result).toStrictEqual({
      file: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
      subdir: {
        nestedFile: 'JSwithRESOLVED_VALUE_3',
      },
    });
  });

  test('should ignore subdirectories when recursive is false', async () => {
    const result = await applyVariables(mockConfig, nestedFiles, {
      isFolder: true,
      recursive: false,
    });
    expect(result).toStrictEqual({
      file: 'dataXMLwithRESOLVED_VALUEandRESOLVED_VALUE_2',
      // subdir should be ignored
    });
    // Variable resolvers should only be called for top-level files
    expect(mockVariableResolvers.VARIABLE_1).toHaveBeenCalledTimes(1);
    expect(mockVariableResolvers.VARIABLE_2).toHaveBeenCalledTimes(1);
    // No calls for nested files
  });
});
