const fs = require('fs');
const path = require('path');
const picomatch = require('picomatch');

const IGNORES = ['__tests__', '__mocks__', '__test-utils__', '*.test.js', '*.spec.js'];
const isIgnored = picomatch(IGNORES);

function getFiles(folderPath, recursive = false) {
  if (!fs.existsSync(folderPath)) {
    // Do not throw an error if the folder does not exist. Resources are constructed in a hierarchy with sub folders used for proposition/territory specific overrides.
    return {};
  }
  const results = fs.readdirSync(folderPath);
  return results.reduce((fileData, file) => {
    if (isIgnored(file)) {
      return fileData;
    }

    const fullPath = path.join(folderPath, file);
    if (fs.statSync(fullPath).isDirectory()) {
      if (recursive) {
        const subDirFiles = getFiles(fullPath, recursive); // Recursively get files in subdirectory
        // Only include the directory if it contains files
        if (Object.keys(subDirFiles).length > 0) {
          fileData[file] = subDirFiles;
        }
      }
      // If not recursive, skip directories
    } else {
      fileData[file] = fs.readFileSync(fullPath);
    }
    return fileData;
  }, {});
}

function getResourceFiles(resource, recursive) {
  return getFiles(path.join(process.cwd(), `resources/${resource}`), recursive);
}

function getPropositionFiles(resource, proposition, recursive) {
  return getFiles(path.join(process.cwd(), `resources/${resource}/${proposition}`), recursive);
}

function getTerritoryFiles(resource, proposition, territory, recursive) {
  return getFiles(
    path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}`),
    recursive
  );
}

function getTerritoryEnvironmentFiles(resource, proposition, territory, environment, recursive) {
  return getFiles(
    path.join(process.cwd(), `resources/${resource}/${proposition}/${territory}/${environment}`),
    recursive
  );
}

function getMergedResourceFiles(resource, proposition, territory, environment, recursive) {
  const resourceFiles = getResourceFiles(resource, recursive);
  const propositionFiles = getPropositionFiles(resource, proposition, recursive);
  const territoryFiles = getTerritoryFiles(resource, proposition, territory, recursive);
  const territoryEnvironmentFiles = getTerritoryEnvironmentFiles(
    resource,
    proposition,
    territory,
    environment,
    recursive
  );

  return {
    ...resourceFiles,
    ...propositionFiles,
    ...territoryFiles,
    ...territoryEnvironmentFiles,
  };
}

function getBaseConfigXML() {
  return fs.readFileSync(path.join(process.cwd(), 'resources/config.xml'), 'utf-8');
}

module.exports = { getMergedResourceFiles, getBaseConfigXML };
